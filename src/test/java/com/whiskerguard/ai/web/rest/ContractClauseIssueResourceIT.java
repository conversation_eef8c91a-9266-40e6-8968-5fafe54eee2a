package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.ContractClauseIssueAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.ContractClauseIssue;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.ContractClauseIssueRepository;
import com.whiskerguard.ai.service.dto.ContractClauseIssueDTO;
import com.whiskerguard.ai.service.mapper.ContractClauseIssueMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContractClauseIssueResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContractClauseIssueResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_REVIEW_ID = 1L;
    private static final Long UPDATED_REVIEW_ID = 2L;

    private static final String DEFAULT_CLAUSE_TEXT = "AAAAAAAAAA";
    private static final String UPDATED_CLAUSE_TEXT = "BBBBBBBBBB";

    private static final String DEFAULT_CLAUSE_NUMBER = "AAAAAAAAAA";
    private static final String UPDATED_CLAUSE_NUMBER = "BBBBBBBBBB";

    private static final String DEFAULT_ISSUE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_ISSUE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_ISSUE_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_ISSUE_DESCRIPTION = "BBBBBBBBBB";

    private static final RiskLevel DEFAULT_SEVERITY = RiskLevel.HIGH;
    private static final RiskLevel UPDATED_SEVERITY = RiskLevel.MEDIUM;

    private static final String DEFAULT_LEGAL_RISK = "AAAAAAAAAA";
    private static final String UPDATED_LEGAL_RISK = "BBBBBBBBBB";

    private static final String DEFAULT_SUGGESTIONS = "AAAAAAAAAA";
    private static final String UPDATED_SUGGESTIONS = "BBBBBBBBBB";

    private static final String DEFAULT_REFERENCE_LAWS = "AAAAAAAAAA";
    private static final String UPDATED_REFERENCE_LAWS = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/contract-clause-issues";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContractClauseIssueRepository contractClauseIssueRepository;

    @Autowired
    private ContractClauseIssueMapper contractClauseIssueMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContractClauseIssueMockMvc;

    private ContractClauseIssue contractClauseIssue;

    private ContractClauseIssue insertedContractClauseIssue;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractClauseIssue createEntity() {
        return new ContractClauseIssue()
            .tenantId(DEFAULT_TENANT_ID)
            .reviewId(DEFAULT_REVIEW_ID)
            .clauseText(DEFAULT_CLAUSE_TEXT)
            .clauseNumber(DEFAULT_CLAUSE_NUMBER)
            .issueType(DEFAULT_ISSUE_TYPE)
            .issueDescription(DEFAULT_ISSUE_DESCRIPTION)
            .severity(DEFAULT_SEVERITY)
            .legalRisk(DEFAULT_LEGAL_RISK)
            .suggestions(DEFAULT_SUGGESTIONS)
            .referenceLaws(DEFAULT_REFERENCE_LAWS)
            .version(DEFAULT_VERSION)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractClauseIssue createUpdatedEntity() {
        return new ContractClauseIssue()
            .tenantId(UPDATED_TENANT_ID)
            .reviewId(UPDATED_REVIEW_ID)
            .clauseText(UPDATED_CLAUSE_TEXT)
            .clauseNumber(UPDATED_CLAUSE_NUMBER)
            .issueType(UPDATED_ISSUE_TYPE)
            .issueDescription(UPDATED_ISSUE_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .legalRisk(UPDATED_LEGAL_RISK)
            .suggestions(UPDATED_SUGGESTIONS)
            .referenceLaws(UPDATED_REFERENCE_LAWS)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        contractClauseIssue = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContractClauseIssue != null) {
            contractClauseIssueRepository.delete(insertedContractClauseIssue);
            insertedContractClauseIssue = null;
        }
    }

    @Test
    @Transactional
    void createContractClauseIssue() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);
        var returnedContractClauseIssueDTO = om.readValue(
            restContractClauseIssueMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContractClauseIssueDTO.class
        );

        // Validate the ContractClauseIssue in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContractClauseIssue = contractClauseIssueMapper.toEntity(returnedContractClauseIssueDTO);
        assertContractClauseIssueUpdatableFieldsEquals(
            returnedContractClauseIssue,
            getPersistedContractClauseIssue(returnedContractClauseIssue)
        );

        insertedContractClauseIssue = returnedContractClauseIssue;
    }

    @Test
    @Transactional
    void createContractClauseIssueWithExistingId() throws Exception {
        // Create the ContractClauseIssue with an existing ID
        contractClauseIssue.setId(1L);
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractClauseIssue.setTenantId(null);

        // Create the ContractClauseIssue, which fails.
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReviewIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractClauseIssue.setReviewId(null);

        // Create the ContractClauseIssue, which fails.
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSeverityIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractClauseIssue.setSeverity(null);

        // Create the ContractClauseIssue, which fails.
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractClauseIssue.setVersion(null);

        // Create the ContractClauseIssue, which fails.
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractClauseIssue.setCreatedAt(null);

        // Create the ContractClauseIssue, which fails.
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractClauseIssue.setIsDeleted(null);

        // Create the ContractClauseIssue, which fails.
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContractClauseIssues() throws Exception {
        // Initialize the database
        insertedContractClauseIssue = contractClauseIssueRepository.saveAndFlush(contractClauseIssue);

        // Get all the contractClauseIssueList
        restContractClauseIssueMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(contractClauseIssue.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].reviewId").value(hasItem(DEFAULT_REVIEW_ID.intValue())))
            .andExpect(jsonPath("$.[*].clauseText").value(hasItem(DEFAULT_CLAUSE_TEXT)))
            .andExpect(jsonPath("$.[*].clauseNumber").value(hasItem(DEFAULT_CLAUSE_NUMBER)))
            .andExpect(jsonPath("$.[*].issueType").value(hasItem(DEFAULT_ISSUE_TYPE)))
            .andExpect(jsonPath("$.[*].issueDescription").value(hasItem(DEFAULT_ISSUE_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].severity").value(hasItem(DEFAULT_SEVERITY.toString())))
            .andExpect(jsonPath("$.[*].legalRisk").value(hasItem(DEFAULT_LEGAL_RISK)))
            .andExpect(jsonPath("$.[*].suggestions").value(hasItem(DEFAULT_SUGGESTIONS)))
            .andExpect(jsonPath("$.[*].referenceLaws").value(hasItem(DEFAULT_REFERENCE_LAWS)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContractClauseIssue() throws Exception {
        // Initialize the database
        insertedContractClauseIssue = contractClauseIssueRepository.saveAndFlush(contractClauseIssue);

        // Get the contractClauseIssue
        restContractClauseIssueMockMvc
            .perform(get(ENTITY_API_URL_ID, contractClauseIssue.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(contractClauseIssue.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.reviewId").value(DEFAULT_REVIEW_ID.intValue()))
            .andExpect(jsonPath("$.clauseText").value(DEFAULT_CLAUSE_TEXT))
            .andExpect(jsonPath("$.clauseNumber").value(DEFAULT_CLAUSE_NUMBER))
            .andExpect(jsonPath("$.issueType").value(DEFAULT_ISSUE_TYPE))
            .andExpect(jsonPath("$.issueDescription").value(DEFAULT_ISSUE_DESCRIPTION))
            .andExpect(jsonPath("$.severity").value(DEFAULT_SEVERITY.toString()))
            .andExpect(jsonPath("$.legalRisk").value(DEFAULT_LEGAL_RISK))
            .andExpect(jsonPath("$.suggestions").value(DEFAULT_SUGGESTIONS))
            .andExpect(jsonPath("$.referenceLaws").value(DEFAULT_REFERENCE_LAWS))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContractClauseIssue() throws Exception {
        // Get the contractClauseIssue
        restContractClauseIssueMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContractClauseIssue() throws Exception {
        // Initialize the database
        insertedContractClauseIssue = contractClauseIssueRepository.saveAndFlush(contractClauseIssue);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractClauseIssue
        ContractClauseIssue updatedContractClauseIssue = contractClauseIssueRepository.findById(contractClauseIssue.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedContractClauseIssue are not directly saved in db
        em.detach(updatedContractClauseIssue);
        updatedContractClauseIssue
            .tenantId(UPDATED_TENANT_ID)
            .reviewId(UPDATED_REVIEW_ID)
            .clauseText(UPDATED_CLAUSE_TEXT)
            .clauseNumber(UPDATED_CLAUSE_NUMBER)
            .issueType(UPDATED_ISSUE_TYPE)
            .issueDescription(UPDATED_ISSUE_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .legalRisk(UPDATED_LEGAL_RISK)
            .suggestions(UPDATED_SUGGESTIONS)
            .referenceLaws(UPDATED_REFERENCE_LAWS)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(updatedContractClauseIssue);

        restContractClauseIssueMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractClauseIssueDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractClauseIssueDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContractClauseIssueToMatchAllProperties(updatedContractClauseIssue);
    }

    @Test
    @Transactional
    void putNonExistingContractClauseIssue() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractClauseIssue.setId(longCount.incrementAndGet());

        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractClauseIssueMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractClauseIssueDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractClauseIssueDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContractClauseIssue() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractClauseIssue.setId(longCount.incrementAndGet());

        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractClauseIssueMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractClauseIssueDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContractClauseIssue() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractClauseIssue.setId(longCount.incrementAndGet());

        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractClauseIssueMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractClauseIssueDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContractClauseIssueWithPatch() throws Exception {
        // Initialize the database
        insertedContractClauseIssue = contractClauseIssueRepository.saveAndFlush(contractClauseIssue);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractClauseIssue using partial update
        ContractClauseIssue partialUpdatedContractClauseIssue = new ContractClauseIssue();
        partialUpdatedContractClauseIssue.setId(contractClauseIssue.getId());

        partialUpdatedContractClauseIssue
            .clauseNumber(UPDATED_CLAUSE_NUMBER)
            .issueDescription(UPDATED_ISSUE_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .legalRisk(UPDATED_LEGAL_RISK)
            .referenceLaws(UPDATED_REFERENCE_LAWS)
            .isDeleted(UPDATED_IS_DELETED);

        restContractClauseIssueMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractClauseIssue.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractClauseIssue))
            )
            .andExpect(status().isOk());

        // Validate the ContractClauseIssue in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractClauseIssueUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContractClauseIssue, contractClauseIssue),
            getPersistedContractClauseIssue(contractClauseIssue)
        );
    }

    @Test
    @Transactional
    void fullUpdateContractClauseIssueWithPatch() throws Exception {
        // Initialize the database
        insertedContractClauseIssue = contractClauseIssueRepository.saveAndFlush(contractClauseIssue);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractClauseIssue using partial update
        ContractClauseIssue partialUpdatedContractClauseIssue = new ContractClauseIssue();
        partialUpdatedContractClauseIssue.setId(contractClauseIssue.getId());

        partialUpdatedContractClauseIssue
            .tenantId(UPDATED_TENANT_ID)
            .reviewId(UPDATED_REVIEW_ID)
            .clauseText(UPDATED_CLAUSE_TEXT)
            .clauseNumber(UPDATED_CLAUSE_NUMBER)
            .issueType(UPDATED_ISSUE_TYPE)
            .issueDescription(UPDATED_ISSUE_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .legalRisk(UPDATED_LEGAL_RISK)
            .suggestions(UPDATED_SUGGESTIONS)
            .referenceLaws(UPDATED_REFERENCE_LAWS)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContractClauseIssueMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractClauseIssue.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractClauseIssue))
            )
            .andExpect(status().isOk());

        // Validate the ContractClauseIssue in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractClauseIssueUpdatableFieldsEquals(
            partialUpdatedContractClauseIssue,
            getPersistedContractClauseIssue(partialUpdatedContractClauseIssue)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContractClauseIssue() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractClauseIssue.setId(longCount.incrementAndGet());

        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractClauseIssueMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, contractClauseIssueDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractClauseIssueDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContractClauseIssue() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractClauseIssue.setId(longCount.incrementAndGet());

        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractClauseIssueMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractClauseIssueDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContractClauseIssue() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractClauseIssue.setId(longCount.incrementAndGet());

        // Create the ContractClauseIssue
        ContractClauseIssueDTO contractClauseIssueDTO = contractClauseIssueMapper.toDto(contractClauseIssue);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractClauseIssueMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(contractClauseIssueDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractClauseIssue in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContractClauseIssue() throws Exception {
        // Initialize the database
        insertedContractClauseIssue = contractClauseIssueRepository.saveAndFlush(contractClauseIssue);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the contractClauseIssue
        restContractClauseIssueMockMvc
            .perform(delete(ENTITY_API_URL_ID, contractClauseIssue.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return contractClauseIssueRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContractClauseIssue getPersistedContractClauseIssue(ContractClauseIssue contractClauseIssue) {
        return contractClauseIssueRepository.findById(contractClauseIssue.getId()).orElseThrow();
    }

    protected void assertPersistedContractClauseIssueToMatchAllProperties(ContractClauseIssue expectedContractClauseIssue) {
        assertContractClauseIssueAllPropertiesEquals(
            expectedContractClauseIssue,
            getPersistedContractClauseIssue(expectedContractClauseIssue)
        );
    }

    protected void assertPersistedContractClauseIssueToMatchUpdatableProperties(ContractClauseIssue expectedContractClauseIssue) {
        assertContractClauseIssueAllUpdatablePropertiesEquals(
            expectedContractClauseIssue,
            getPersistedContractClauseIssue(expectedContractClauseIssue)
        );
    }
}
