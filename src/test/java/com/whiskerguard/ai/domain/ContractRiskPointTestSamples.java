package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ContractRiskPointTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static ContractRiskPoint getContractRiskPointSample1() {
        return new ContractRiskPoint().id(1L).tenantId(1L).reviewId(1L).riskScore(1).riskSource("riskSource1").version(1);
    }

    public static ContractRiskPoint getContractRiskPointSample2() {
        return new ContractRiskPoint().id(2L).tenantId(2L).reviewId(2L).riskScore(2).riskSource("riskSource2").version(2);
    }

    public static ContractRiskPoint getContractRiskPointRandomSampleGenerator() {
        return new ContractRiskPoint()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .reviewId(longCount.incrementAndGet())
            .riskScore(intCount.incrementAndGet())
            .riskSource(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet());
    }
}
