package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ContractPartyAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractPartyAllPropertiesEquals(ContractParty expected, ContractParty actual) {
        assertContractPartyAutoGeneratedPropertiesEquals(expected, actual);
        assertContractPartyAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractPartyAllUpdatablePropertiesEquals(ContractParty expected, ContractParty actual) {
        assertContractPartyUpdatableFieldsEquals(expected, actual);
        assertContractPartyUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractPartyAutoGeneratedPropertiesEquals(ContractParty expected, ContractParty actual) {
        assertThat(actual)
            .as("Verify ContractParty auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractPartyUpdatableFieldsEquals(ContractParty expected, ContractParty actual) {
        assertThat(actual)
            .as("Verify ContractParty relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getReviewId()).as("check reviewId").isEqualTo(expected.getReviewId()))
            .satisfies(a -> assertThat(a.getPartyName()).as("check partyName").isEqualTo(expected.getPartyName()))
            .satisfies(a -> assertThat(a.getPartyType()).as("check partyType").isEqualTo(expected.getPartyType()))
            .satisfies(a -> assertThat(a.getPartyRole()).as("check partyRole").isEqualTo(expected.getPartyRole()))
            .satisfies(a -> assertThat(a.getCreditCode()).as("check creditCode").isEqualTo(expected.getCreditCode()))
            .satisfies(a -> assertThat(a.getRegisteredAddress()).as("check registeredAddress").isEqualTo(expected.getRegisteredAddress()))
            .satisfies(a ->
                assertThat(a.getLegalRepresentative()).as("check legalRepresentative").isEqualTo(expected.getLegalRepresentative())
            )
            .satisfies(a -> assertThat(a.getContactInfo()).as("check contactInfo").isEqualTo(expected.getContactInfo()))
            .satisfies(a -> assertThat(a.getRiskLevel()).as("check riskLevel").isEqualTo(expected.getRiskLevel()))
            .satisfies(a -> assertThat(a.getRiskFactors()).as("check riskFactors").isEqualTo(expected.getRiskFactors()))
            .satisfies(a -> assertThat(a.getComplianceIssues()).as("check complianceIssues").isEqualTo(expected.getComplianceIssues()))
            .satisfies(a -> assertThat(a.getTianyanchaInfo()).as("check tianyanchaInfo").isEqualTo(expected.getTianyanchaInfo()))
            .satisfies(a -> assertThat(a.getAdditionalInfo()).as("check additionalInfo").isEqualTo(expected.getAdditionalInfo()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractPartyUpdatableRelationshipsEquals(ContractParty expected, ContractParty actual) {
        assertThat(actual)
            .as("Verify ContractParty relationships")
            .satisfies(a -> assertThat(a.getReview()).as("check review").isEqualTo(expected.getReview()));
    }
}
