package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ContractPartyTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static ContractParty getContractPartySample1() {
        return new ContractParty()
            .id(1L)
            .tenantId(1L)
            .reviewId(1L)
            .partyName("partyName1")
            .partyRole("partyRole1")
            .creditCode("creditCode1")
            .registeredAddress("registeredAddress1")
            .legalRepresentative("legalRepresentative1")
            .contactInfo("contactInfo1")
            .version(1);
    }

    public static ContractParty getContractPartySample2() {
        return new ContractParty()
            .id(2L)
            .tenantId(2L)
            .reviewId(2L)
            .partyName("partyName2")
            .partyRole("partyRole2")
            .creditCode("creditCode2")
            .registeredAddress("registeredAddress2")
            .legalRepresentative("legalRepresentative2")
            .contactInfo("contactInfo2")
            .version(2);
    }

    public static ContractParty getContractPartyRandomSampleGenerator() {
        return new ContractParty()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .reviewId(longCount.incrementAndGet())
            .partyName(UUID.randomUUID().toString())
            .partyRole(UUID.randomUUID().toString())
            .creditCode(UUID.randomUUID().toString())
            .registeredAddress(UUID.randomUUID().toString())
            .legalRepresentative(UUID.randomUUID().toString())
            .contactInfo(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet());
    }
}
