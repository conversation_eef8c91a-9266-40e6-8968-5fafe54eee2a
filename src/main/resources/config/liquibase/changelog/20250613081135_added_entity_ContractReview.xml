<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ContractReview.
    -->
    <changeSet id="20250613081135-1" author="jhipster">
        <createTable tableName="contract_review" remarks="合同审查记录实体\n存储合同审查的基本信息和结果">
            <column name="id" type="bigint" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID - 多租户数据隔离">
                <constraints nullable="false" />
            </column>
            <column name="employee_id" type="bigint" remarks="员工ID">
                <constraints nullable="false" />
            </column>
            <column name="contract_type" type="varchar(64)" remarks="合同类型">
                <constraints nullable="true" />
            </column>
            <column name="contract_title" type="varchar(256)" remarks="合同标题">
                <constraints nullable="true" />
            </column>
            <column name="contract_content" type="${clobType}" remarks="合同内容">
                <constraints nullable="false" />
            </column>
            <column name="review_result" type="${clobType}" remarks="审查结果（JSON格式）">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)" remarks="审查状态">
                <constraints nullable="false" />
            </column>
            <column name="overall_risk_level" type="varchar(255)" remarks="整体风险等级">
                <constraints nullable="true" />
            </column>
            <column name="risk_score" type="integer" remarks="风险分数 (0-100)">
                <constraints nullable="true" />
            </column>
            <column name="risk_summary" type="${clobType}" remarks="风险总结">
                <constraints nullable="true" />
            </column>
            <column name="ai_request_id" type="bigint" remarks="AI调用ID - 关联到ai_request表">
                <constraints nullable="true" />
            </column>
            <column name="review_start_time" type="${datetimeType}" remarks="审查开始时间">
                <constraints nullable="true" />
            </column>
            <column name="review_end_time" type="${datetimeType}" remarks="审查完成时间">
                <constraints nullable="true" />
            </column>
            <column name="review_duration" type="bigint" remarks="审查耗时（毫秒）">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="contract_review" columnName="review_start_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="contract_review" columnName="review_end_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="contract_review" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="contract_review" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250613081135-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/contract_review.csv"
                  separator=";"
                  tableName="contract_review"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="employee_id" type="numeric"/>
            <column name="contract_type" type="string"/>
            <column name="contract_title" type="string"/>
            <column name="contract_content" type="clob"/>
            <column name="review_result" type="clob"/>
            <column name="status" type="string"/>
            <column name="overall_risk_level" type="string"/>
            <column name="risk_score" type="numeric"/>
            <column name="risk_summary" type="clob"/>
            <column name="ai_request_id" type="numeric"/>
            <column name="review_start_time" type="date"/>
            <column name="review_end_time" type="date"/>
            <column name="review_duration" type="numeric"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
