/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.dto
 * 描    述：合同智能审查请求数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * 合同智能审查请求数据传输对象
 * <p>
 * 用于封装合同智能审查的请求参数，
 * 支持多租户SaaS服务的数据隔离。
 */
public class ContractReviewRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 合同内容 - 必填 */
    @NotBlank(message = "合同内容不能为空")
    @Size(min = 10, max = 1000000, message = "合同内容长度必须在10到1000000字符之间")
    private String contractContent;

    /** 合同类型 - 必填 */
    @NotBlank(message = "合同类型不能为空")
    @Size(max = 64, message = "合同类型长度不能超过64字符")
    private String contractType;

    /** 合同标题 - 可选 */
    @Size(max = 256, message = "合同标题长度不能超过256字符")
    private String contractTitle;

    /** 租户ID - 必填，用于多租户数据隔离 */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /** 员工ID - 必填，用于审计和权限控制 */
    @NotNull(message = "员工ID不能为空")
    private Long employeeId;

    /** 扩展元数据 - 可选 */
    private Map<String, Object> metadata;

    /** 审查优先级 - 可选 */
    private ReviewPriority priority;

    /** 是否启用深度分析 - 可选，默认true */
    private Boolean enableDeepAnalysis;

    /** 是否包含企业背景调查 - 可选，默认true */
    private Boolean includeCompanyBackground;

    /** 是否检查内部制度合规性 - 可选，默认true */
    private Boolean checkInternalPolicies;

    /** 指定的审查重点 - 可选 */
    private ReviewFocus reviewFocus;

    /** 风险容忍度 - 可选 */
    private RiskTolerance riskTolerance;

    /** 行业类别 - 可选，用于行业特定的合规检查 */
    private String industryCategory;

    /** 合同金额 - 可选，用于风险评估 */
    private String contractAmount;

    /** 合同期限 - 可选 */
    private String contractTerm;

    /** 特殊要求说明 - 可选 */
    private String specialRequirements;

    public ContractReviewRequestDTO() {
        this.priority = ReviewPriority.NORMAL;
        this.enableDeepAnalysis = true;
        this.includeCompanyBackground = true;
        this.checkInternalPolicies = true;
        this.reviewFocus = ReviewFocus.COMPREHENSIVE;
        this.riskTolerance = RiskTolerance.MEDIUM;
    }

    public ContractReviewRequestDTO(String contractContent, String contractType, Long tenantId, Long employeeId) {
        this();
        this.contractContent = contractContent;
        this.contractType = contractType;
        this.tenantId = tenantId;
        this.employeeId = employeeId;
    }

    // 枚举：审查优先级
    public enum ReviewPriority {
        LOW,      // 低优先级
        NORMAL,   // 普通优先级
        HIGH,     // 高优先级
        URGENT    // 紧急优先级
    }

    // 枚举：审查重点
    public enum ReviewFocus {
        COMPREHENSIVE,    // 全面审查
        LEGAL_ONLY,      // 仅法律合规
        RISK_ONLY,       // 仅风险评估
        FINANCIAL_ONLY,  // 仅财务条款
        OPERATIONAL_ONLY // 仅操作条款
    }

    // 枚举：风险容忍度
    public enum RiskTolerance {
        LOW,     // 低风险容忍度 - 严格审查
        MEDIUM,  // 中等风险容忍度 - 标准审查
        HIGH     // 高风险容忍度 - 宽松审查
    }

    // Getters and Setters
    public String getContractContent() {
        return contractContent;
    }

    public void setContractContent(String contractContent) {
        this.contractContent = contractContent;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractTitle() {
        return contractTitle;
    }

    public void setContractTitle(String contractTitle) {
        this.contractTitle = contractTitle;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public ReviewPriority getPriority() {
        return priority;
    }

    public void setPriority(ReviewPriority priority) {
        this.priority = priority;
    }

    public Boolean getEnableDeepAnalysis() {
        return enableDeepAnalysis;
    }

    public void setEnableDeepAnalysis(Boolean enableDeepAnalysis) {
        this.enableDeepAnalysis = enableDeepAnalysis;
    }

    public Boolean getIncludeCompanyBackground() {
        return includeCompanyBackground;
    }

    public void setIncludeCompanyBackground(Boolean includeCompanyBackground) {
        this.includeCompanyBackground = includeCompanyBackground;
    }

    public Boolean getCheckInternalPolicies() {
        return checkInternalPolicies;
    }

    public void setCheckInternalPolicies(Boolean checkInternalPolicies) {
        this.checkInternalPolicies = checkInternalPolicies;
    }

    public ReviewFocus getReviewFocus() {
        return reviewFocus;
    }

    public void setReviewFocus(ReviewFocus reviewFocus) {
        this.reviewFocus = reviewFocus;
    }

    public RiskTolerance getRiskTolerance() {
        return riskTolerance;
    }

    public void setRiskTolerance(RiskTolerance riskTolerance) {
        this.riskTolerance = riskTolerance;
    }

    public String getIndustryCategory() {
        return industryCategory;
    }

    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    public String getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(String contractAmount) {
        this.contractAmount = contractAmount;
    }

    public String getContractTerm() {
        return contractTerm;
    }

    public void setContractTerm(String contractTerm) {
        this.contractTerm = contractTerm;
    }

    public String getSpecialRequirements() {
        return specialRequirements;
    }

    public void setSpecialRequirements(String specialRequirements) {
        this.specialRequirements = specialRequirements;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContractReviewRequestDTO that = (ContractReviewRequestDTO) o;
        return Objects.equals(tenantId, that.tenantId) && 
               Objects.equals(employeeId, that.employeeId) &&
               Objects.equals(contractContent, that.contractContent);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tenantId, employeeId, contractContent);
    }

    @Override
    public String toString() {
        return "ContractReviewRequestDTO{" +
            "contractType='" + contractType + '\'' +
            ", contractTitle='" + contractTitle + '\'' +
            ", tenantId=" + tenantId +
            ", employeeId=" + employeeId +
            ", priority=" + priority +
            ", enableDeepAnalysis=" + enableDeepAnalysis +
            ", includeCompanyBackground=" + includeCompanyBackground +
            ", checkInternalPolicies=" + checkInternalPolicies +
            ", reviewFocus=" + reviewFocus +
            ", riskTolerance=" + riskTolerance +
            ", industryCategory='" + industryCategory + '\'' +
            ", contractAmount='" + contractAmount + '\'' +
            ", contractTerm='" + contractTerm + '\'' +
            '}';
    }
}
