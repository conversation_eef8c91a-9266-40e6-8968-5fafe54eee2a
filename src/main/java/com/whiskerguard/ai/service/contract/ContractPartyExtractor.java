/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractPartyExtractor.java
 * 包    名：com.whiskerguard.ai.service.contract
 * 描    述：合同关联方提取器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract;

import com.whiskerguard.ai.domain.enumeration.PartyType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 合同关联方提取器
 * <p>
 * 使用正则表达式和自然语言处理技术从合同文本中提取关联方信息，
 * 包括企业名称、个人姓名等，并识别其在合同中的角色。
 */
@Component
public class ContractPartyExtractor {

    private final Logger log = LoggerFactory.getLogger(ContractPartyExtractor.class);

    // 企业名称匹配模式
    private static final Pattern COMPANY_PATTERN = Pattern.compile(
        "([\\u4e00-\\u9fa5A-Za-z0-9\\(\\)（）]{2,50}(?:有限公司|股份有限公司|有限责任公司|集团有限公司|" +
        "集团股份有限公司|科技有限公司|贸易有限公司|投资有限公司|控股有限公司|实业有限公司|" +
        "建设有限公司|工程有限公司|咨询有限公司|服务有限公司|发展有限公司|" +
        "集团|公司|企业|厂|所|院|中心|基金|银行|保险|证券|信托))"
    );

    // 个人姓名匹配模式（中文姓名）
    private static final Pattern PERSON_PATTERN = Pattern.compile(
        "(?:先生|女士|经理|总监|董事长|总经理|法定代表人|负责人|联系人)[:：]?\\s*([\\u4e00-\\u9fa5]{2,4})|" +
        "([\\u4e00-\\u9fa5]{2,4})(?:\\s*先生|\\s*女士|\\s*经理|\\s*总监|\\s*董事长|\\s*总经理)"
    );

    // 角色关键词模式
    private static final Pattern ROLE_PATTERN = Pattern.compile(
        "(甲方|乙方|丙方|丁方|委托方|受托方|买方|卖方|供应商|采购方|承包方|发包方|" +
        "出租方|承租方|借款方|贷款方|投资方|被投资方|服务方|客户方|合作方)"
    );

    /**
     * 从合同内容中提取关联方信息
     *
     * @param contractContent 合同内容
     * @return 关联方列表
     */
    public List<ContractPartyInfo> extractParties(String contractContent) {
        log.debug("开始提取合同关联方信息，合同内容长度: {}", contractContent.length());

        List<ContractPartyInfo> parties = new ArrayList<>();
        Set<String> extractedNames = new HashSet<>(); // 避免重复

        try {
            // 1. 提取企业名称
            List<ContractPartyInfo> companies = extractCompanies(contractContent);
            for (ContractPartyInfo company : companies) {
                if (!extractedNames.contains(company.getName())) {
                    parties.add(company);
                    extractedNames.add(company.getName());
                }
            }

            // 2. 提取个人姓名
            List<ContractPartyInfo> individuals = extractIndividuals(contractContent);
            for (ContractPartyInfo individual : individuals) {
                if (!extractedNames.contains(individual.getName())) {
                    parties.add(individual);
                    extractedNames.add(individual.getName());
                }
            }

            // 3. 为关联方分配角色
            assignRoles(parties, contractContent);

            log.debug("关联方提取完成，共提取到 {} 个关联方", parties.size());

        } catch (Exception e) {
            log.error("提取合同关联方时发生错误", e);
        }

        return parties;
    }

    /**
     * 提取企业名称
     */
    private List<ContractPartyInfo> extractCompanies(String content) {
        List<ContractPartyInfo> companies = new ArrayList<>();
        Matcher matcher = COMPANY_PATTERN.matcher(content);

        while (matcher.find()) {
            String companyName = matcher.group(1).trim();
            
            // 过滤掉过短或明显不是企业名称的结果
            if (isValidCompanyName(companyName)) {
                ContractPartyInfo party = new ContractPartyInfo();
                party.setName(companyName);
                party.setType(PartyType.COMPANY);
                party.setExtractedPosition(matcher.start());
                companies.add(party);
                
                log.debug("提取到企业: {}", companyName);
            }
        }

        return companies;
    }

    /**
     * 提取个人姓名
     */
    private List<ContractPartyInfo> extractIndividuals(String content) {
        List<ContractPartyInfo> individuals = new ArrayList<>();
        Matcher matcher = PERSON_PATTERN.matcher(content);

        while (matcher.find()) {
            String personName = null;
            
            // 检查两个捕获组
            if (matcher.group(1) != null) {
                personName = matcher.group(1).trim();
            } else if (matcher.group(2) != null) {
                personName = matcher.group(2).trim();
            }

            if (personName != null && isValidPersonName(personName)) {
                ContractPartyInfo party = new ContractPartyInfo();
                party.setName(personName);
                party.setType(PartyType.INDIVIDUAL);
                party.setExtractedPosition(matcher.start());
                individuals.add(party);
                
                log.debug("提取到个人: {}", personName);
            }
        }

        return individuals;
    }

    /**
     * 为关联方分配角色
     */
    private void assignRoles(List<ContractPartyInfo> parties, String content) {
        for (ContractPartyInfo party : parties) {
            String role = determineRole(party.getName(), content);
            party.setRole(role);
            
            // 提取更多上下文信息
            String context = extractContext(party.getName(), content);
            party.setContext(context);
        }
    }

    /**
     * 确定关联方在合同中的角色
     */
    private String determineRole(String partyName, String content) {
        // 在关联方名称附近查找角色关键词
        String[] lines = content.split("\n");
        
        for (String line : lines) {
            if (line.contains(partyName)) {
                Matcher roleMatcher = ROLE_PATTERN.matcher(line);
                if (roleMatcher.find()) {
                    return roleMatcher.group(1);
                }
            }
        }
        
        // 如果没有找到明确角色，尝试从上下文推断
        return inferRoleFromContext(partyName, content);
    }

    /**
     * 从上下文推断角色
     */
    private String inferRoleFromContext(String partyName, String content) {
        String lowerContent = content.toLowerCase();
        String lowerName = partyName.toLowerCase();
        
        // 查找关联方名称在文档中的位置
        int nameIndex = lowerContent.indexOf(lowerName);
        if (nameIndex == -1) {
            return "未知角色";
        }
        
        // 提取前后文本进行分析
        int start = Math.max(0, nameIndex - 100);
        int end = Math.min(content.length(), nameIndex + partyName.length() + 100);
        String context = content.substring(start, end);
        
        // 基于关键词推断角色
        if (context.contains("甲方") || context.contains("委托方") || context.contains("买方")) {
            return "甲方";
        } else if (context.contains("乙方") || context.contains("受托方") || context.contains("卖方")) {
            return "乙方";
        } else if (context.contains("丙方")) {
            return "丙方";
        }
        
        return "合同方";
    }

    /**
     * 提取关联方的上下文信息
     */
    private String extractContext(String partyName, String content) {
        int nameIndex = content.indexOf(partyName);
        if (nameIndex == -1) {
            return "";
        }
        
        // 提取关联方名称前后的文本作为上下文
        int start = Math.max(0, nameIndex - 50);
        int end = Math.min(content.length(), nameIndex + partyName.length() + 50);
        
        return content.substring(start, end).trim();
    }

    /**
     * 验证企业名称的有效性
     */
    private boolean isValidCompanyName(String name) {
        if (name == null || name.length() < 3 || name.length() > 50) {
            return false;
        }
        
        // 排除一些明显不是企业名称的词汇
        String[] invalidWords = {"有限公司", "股份有限公司", "公司", "企业", "集团"};
        for (String word : invalidWords) {
            if (name.equals(word)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 验证个人姓名的有效性
     */
    private boolean isValidPersonName(String name) {
        if (name == null || name.length() < 2 || name.length() > 4) {
            return false;
        }
        
        // 排除一些明显不是姓名的词汇
        String[] invalidWords = {"先生", "女士", "经理", "总监", "董事长", "总经理", "法定", "代表", "负责", "联系"};
        for (String word : invalidWords) {
            if (name.contains(word)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 合同关联方信息内部类
     */
    public static class ContractPartyInfo {
        private String name;
        private PartyType type;
        private String role;
        private String context;
        private int extractedPosition;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public PartyType getType() { return type; }
        public void setType(PartyType type) { this.type = type; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        
        public String getContext() { return context; }
        public void setContext(String context) { this.context = context; }
        
        public int getExtractedPosition() { return extractedPosition; }
        public void setExtractedPosition(int extractedPosition) { this.extractedPosition = extractedPosition; }

        @Override
        public String toString() {
            return "ContractPartyInfo{" +
                "name='" + name + '\'' +
                ", type=" + type +
                ", role='" + role + '\'' +
                '}';
        }
    }
}
