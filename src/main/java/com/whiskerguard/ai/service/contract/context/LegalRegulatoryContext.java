/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：LegalRegulatoryContext.java
 * 包    名：com.whiskerguard.ai.service.contract.context
 * 描    述：法律法规上下文管理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract.context;

import com.whiskerguard.ai.client.dto.ComplianceChecklistDTO;
import com.whiskerguard.ai.client.dto.LegalRegulationDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 法律法规上下文管理器
 * <p>
 * 整合来自法规制度服务和RAG检索的法律法规信息，
 * 为合同审查提供全面的法律合规依据。
 */
public class LegalRegulatoryContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 合同类型相关法规 */
    private List<LegalRegulationDTO> contractTypeRegulations = new ArrayList<>();

    /** 行业相关法规映射 */
    private Map<String, List<LegalRegulationDTO>> industryRegulationsMap = new HashMap<>();

    /** 合规检查清单 */
    private ComplianceChecklistDTO complianceChecklist;

    /** RAG补充法规信息 */
    private List<RetrieveResponseDTO.Result> ragSupplementaryInfo = new ArrayList<>();

    public LegalRegulatoryContext() {}

    public LegalRegulatoryContext(List<RetrieveResponseDTO.Result> legalResults,
                                List<RetrieveResponseDTO.Result> caseResults) {
        this.ragSupplementaryInfo.addAll(legalResults);
        this.ragSupplementaryInfo.addAll(caseResults);
    }

    /**
     * 设置合同类型相关法规
     */
    public void setContractTypeRegulations(List<LegalRegulationDTO> regulations) {
        this.contractTypeRegulations = regulations != null ? regulations : new ArrayList<>();
    }

    /**
     * 添加行业相关法规
     */
    public void addIndustryRegulations(String industry, List<LegalRegulationDTO> regulations) {
        if (industry != null && regulations != null) {
            industryRegulationsMap.put(industry, regulations);
        }
    }

    /**
     * 设置合规检查清单
     */
    public void setComplianceChecklist(ComplianceChecklistDTO checklist) {
        this.complianceChecklist = checklist;
    }

    /**
     * 设置RAG补充信息
     */
    public void setRagSupplementaryInfo(List<RetrieveResponseDTO.Result> ragInfo) {
        this.ragSupplementaryInfo = ragInfo != null ? ragInfo : new ArrayList<>();
    }

    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return contractTypeRegulations.isEmpty() && 
               industryRegulationsMap.isEmpty() && 
               complianceChecklist == null && 
               ragSupplementaryInfo.isEmpty();
    }

    /**
     * 格式化合同类型法规
     */
    public String formatContractTypeRegulations() {
        if (contractTypeRegulations.isEmpty()) {
            return "暂无相关法规";
        }

        StringBuilder sb = new StringBuilder();
        for (LegalRegulationDTO regulation : contractTypeRegulations) {
            sb.append("- **").append(regulation.getName()).append("**\n");
            sb.append("  法规类型：").append(regulation.getRegulationType()).append("\n");
            sb.append("  发布机关：").append(regulation.getIssuingAuthority()).append("\n");
            sb.append("  重要程度：").append(regulation.getImportance()).append("/5\n");
            
            if (regulation.getSummary() != null) {
                sb.append("  摘要：").append(truncateText(regulation.getSummary(), 150)).append("\n");
            }
            
            if (regulation.getKeyProvisions() != null) {
                sb.append("  关键条款：").append(truncateText(regulation.getKeyProvisions(), 200)).append("\n");
            }
            
            if (regulation.getViolationConsequences() != null) {
                sb.append("  违规后果：").append(truncateText(regulation.getViolationConsequences(), 100)).append("\n");
            }
            
            sb.append("\n");
        }
        
        return sb.toString().trim();
    }

    /**
     * 格式化行业法规
     */
    public String formatIndustryRegulations() {
        if (industryRegulationsMap.isEmpty()) {
            return "暂无行业专门法规";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<LegalRegulationDTO>> entry : industryRegulationsMap.entrySet()) {
            sb.append("**").append(entry.getKey()).append("行业法规：**\n");
            
            for (LegalRegulationDTO regulation : entry.getValue()) {
                sb.append("- ").append(regulation.getName());
                if (regulation.getImportance() != null) {
                    sb.append("（重要程度：").append(regulation.getImportance()).append("/5）");
                }
                sb.append("\n");
                
                if (regulation.getSummary() != null) {
                    sb.append("  ").append(truncateText(regulation.getSummary(), 100)).append("\n");
                }
            }
            sb.append("\n");
        }
        
        return sb.toString().trim();
    }

    /**
     * 格式化合规检查清单
     */
    public String formatComplianceChecklist() {
        if (complianceChecklist == null || complianceChecklist.getCheckItems() == null) {
            return "暂无合规检查清单";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("**合规检查要点：**\n");
        
        List<ComplianceChecklistDTO.CheckItem> mandatoryItems = new ArrayList<>();
        List<ComplianceChecklistDTO.CheckItem> recommendedItems = new ArrayList<>();
        
        // 分类检查项目
        for (ComplianceChecklistDTO.CheckItem item : complianceChecklist.getCheckItems()) {
            if (Boolean.TRUE.equals(item.getMandatory())) {
                mandatoryItems.add(item);
            } else {
                recommendedItems.add(item);
            }
        }
        
        // 必检项目
        if (!mandatoryItems.isEmpty()) {
            sb.append("【必检项目】\n");
            for (ComplianceChecklistDTO.CheckItem item : mandatoryItems) {
                sb.append("- **").append(item.getItemName()).append("**\n");
                sb.append("  ").append(item.getDescription()).append("\n");
                
                if (item.getCheckPoints() != null && !item.getCheckPoints().isEmpty()) {
                    sb.append("  检查要点：");
                    for (String point : item.getCheckPoints()) {
                        sb.append(point).append("；");
                    }
                    sb.append("\n");
                }
                
                if (item.getViolationConsequences() != null) {
                    sb.append("  违规后果：").append(item.getViolationConsequences()).append("\n");
                }
                sb.append("\n");
            }
        }
        
        // 建议检查项目
        if (!recommendedItems.isEmpty()) {
            sb.append("【建议检查项目】\n");
            for (ComplianceChecklistDTO.CheckItem item : recommendedItems) {
                sb.append("- ").append(item.getItemName());
                if (item.getRiskLevel() != null) {
                    sb.append("（风险等级：").append(item.getRiskLevel()).append("）");
                }
                sb.append("\n");
            }
        }
        
        return sb.toString().trim();
    }

    /**
     * 格式化RAG补充信息
     */
    public String formatRagSupplementaryInfo() {
        if (ragSupplementaryInfo.isEmpty()) {
            return "暂无补充法规信息";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("**相关法规解释和案例：**\n");
        
        for (RetrieveResponseDTO.Result result : ragSupplementaryInfo) {
            sb.append("- ").append(truncateText(result.getContent(), 150)).append("\n");
        }
        
        return sb.toString().trim();
    }

    /**
     * 获取所有高重要性法规
     */
    public List<LegalRegulationDTO> getHighImportanceRegulations() {
        List<LegalRegulationDTO> highImportanceRegs = new ArrayList<>();
        
        // 从合同类型法规中筛选
        for (LegalRegulationDTO reg : contractTypeRegulations) {
            if (reg.getImportance() != null && reg.getImportance() >= 4) {
                highImportanceRegs.add(reg);
            }
        }
        
        // 从行业法规中筛选
        for (List<LegalRegulationDTO> regs : industryRegulationsMap.values()) {
            for (LegalRegulationDTO reg : regs) {
                if (reg.getImportance() != null && reg.getImportance() >= 4) {
                    highImportanceRegs.add(reg);
                }
            }
        }
        
        return highImportanceRegs;
    }

    /**
     * 获取必检合规项目数量
     */
    public int getMandatoryComplianceItemCount() {
        if (complianceChecklist == null || complianceChecklist.getCheckItems() == null) {
            return 0;
        }
        
        return (int) complianceChecklist.getCheckItems().stream()
            .filter(item -> Boolean.TRUE.equals(item.getMandatory()))
            .count();
    }

    /**
     * 截断文本到指定长度
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "无";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + "...";
    }

    // Getters
    public List<LegalRegulationDTO> getContractTypeRegulations() {
        return contractTypeRegulations;
    }

    public Map<String, List<LegalRegulationDTO>> getIndustryRegulationsMap() {
        return industryRegulationsMap;
    }

    public ComplianceChecklistDTO getComplianceChecklist() {
        return complianceChecklist;
    }

    public List<RetrieveResponseDTO.Result> getRagSupplementaryInfo() {
        return ragSupplementaryInfo;
    }

    @Override
    public String toString() {
        return "LegalRegulatoryContext{" +
            "contractTypeRegulationCount=" + contractTypeRegulations.size() +
            ", industryCount=" + industryRegulationsMap.size() +
            ", hasComplianceChecklist=" + (complianceChecklist != null) +
            ", ragResultCount=" + ragSupplementaryInfo.size() +
            '}';
    }
}
