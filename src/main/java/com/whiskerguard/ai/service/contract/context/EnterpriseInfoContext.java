/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：EnterpriseInfoContext.java
 * 包    名：com.whiskerguard.ai.service.contract.context
 * 描    述：企业信息上下文管理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract.context;

import com.whiskerguard.ai.client.dto.CompanyBasicInfoDTO;
import com.whiskerguard.ai.client.dto.CompanyCreditInfoDTO;
import com.whiskerguard.ai.client.dto.CompanyLawsuitInfoDTO;
import com.whiskerguard.ai.client.dto.CompanyRiskInfoDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业信息上下文管理器
 * <p>
 * 整合来自天眼查服务和RAG检索的企业信息，
 * 为合同审查提供全面的企业背景数据。
 */
public class EnterpriseInfoContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 企业信息映射表 */
    private final Map<String, EnterpriseInfo> enterpriseInfoMap = new HashMap<>();

    /**
     * 添加企业信息
     */
    public void addEnterpriseInfo(EnterpriseInfo enterpriseInfo) {
        if (enterpriseInfo != null && enterpriseInfo.getCompanyName() != null) {
            enterpriseInfoMap.put(enterpriseInfo.getCompanyName(), enterpriseInfo);
        }
    }

    /**
     * 获取企业信息
     */
    public EnterpriseInfo getEnterpriseInfo(String companyName) {
        return enterpriseInfoMap.get(companyName);
    }

    /**
     * 获取所有企业信息
     */
    public List<EnterpriseInfo> getAllEnterprises() {
        return new ArrayList<>(enterpriseInfoMap.values());
    }

    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return enterpriseInfoMap.isEmpty();
    }

    /**
     * 获取企业数量
     */
    public int getEnterpriseCount() {
        return enterpriseInfoMap.size();
    }

    /**
     * 格式化为提示词文本
     */
    public String formatForPrompt() {
        if (isEmpty()) {
            return "暂无企业信息";
        }

        StringBuilder sb = new StringBuilder();
        for (EnterpriseInfo info : getAllEnterprises()) {
            sb.append(formatSingleEnterpriseInfo(info)).append("\n\n");
        }
        return sb.toString().trim();
    }

    /**
     * 格式化单个企业信息
     */
    private String formatSingleEnterpriseInfo(EnterpriseInfo info) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("**").append(info.getCompanyName()).append("**\n");

        // 基本信息
        if (info.getBasicInfo() != null) {
            CompanyBasicInfoDTO basic = info.getBasicInfo();
            sb.append("【基本信息】\n");
            sb.append("- 统一社会信用代码：").append(basic.getCreditCode()).append("\n");
            sb.append("- 法定代表人：").append(basic.getLegalRepresentative()).append("\n");
            sb.append("- 注册资本：").append(basic.getRegisteredCapital()).append("\n");
            sb.append("- 成立日期：").append(basic.getEstablishDate()).append("\n");
            sb.append("- 经营状态：").append(basic.getStatus()).append("\n");
            sb.append("- 所属行业：").append(basic.getIndustry()).append("\n");
            sb.append("- 经营范围：").append(truncateText(basic.getBusinessScope(), 200)).append("\n");
        }

        // 风险信息
        if (info.getRiskInfo() != null) {
            CompanyRiskInfoDTO risk = info.getRiskInfo();
            sb.append("【风险信息】\n");
            sb.append("- 风险等级：").append(risk.getRiskLevel()).append("\n");
            sb.append("- 风险分数：").append(risk.getRiskScore()).append("分\n");
            sb.append("- 经营异常记录：").append(risk.getAbnormalCount()).append("项\n");
            sb.append("- 行政处罚记录：").append(risk.getPenaltyCount()).append("项\n");
            sb.append("- 严重违法失信：").append(risk.getSeriousViolationCount()).append("项\n");
            if (risk.getRiskSummary() != null) {
                sb.append("- 风险评估：").append(truncateText(risk.getRiskSummary(), 150)).append("\n");
            }
        }

        // 诉讼信息
        if (info.getLawsuitInfo() != null) {
            CompanyLawsuitInfoDTO lawsuit = info.getLawsuitInfo();
            sb.append("【司法风险】\n");
            sb.append("- 法律诉讼：").append(lawsuit.getLawsuitCount()).append("件\n");
            sb.append("- 作为原告：").append(lawsuit.getPlaintiffCount()).append("件\n");
            sb.append("- 作为被告：").append(lawsuit.getDefendantCount()).append("件\n");
            sb.append("- 被执行人：").append(lawsuit.getExecutedCount()).append("次\n");
            sb.append("- 失信被执行人：").append(lawsuit.getDishonestyCount()).append("次\n");
        }

        // 信用信息
        if (info.getCreditInfo() != null) {
            CompanyCreditInfoDTO credit = info.getCreditInfo();
            sb.append("【信用状况】\n");
            sb.append("- 信用等级：").append(credit.getCreditLevel()).append("\n");
            sb.append("- 信用分数：").append(credit.getCreditScore()).append("分\n");
            sb.append("- 失信记录：").append(credit.getDishonestyCount()).append("条\n");
            sb.append("- 信用状态：").append(credit.getCreditStatus()).append("\n");
        }

        // RAG补充信息
        if (info.getRagSupplementaryInfo() != null && !info.getRagSupplementaryInfo().isEmpty()) {
            sb.append("【补充信息】\n");
            for (RetrieveResponseDTO.Result result : info.getRagSupplementaryInfo()) {
                sb.append("- ").append(truncateText(result.getContent(), 100)).append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 截断文本到指定长度
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "无";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + "...";
    }

    /**
     * 企业信息聚合类
     */
    public static class EnterpriseInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String companyName;
        private CompanyBasicInfoDTO basicInfo;
        private CompanyRiskInfoDTO riskInfo;
        private CompanyLawsuitInfoDTO lawsuitInfo;
        private CompanyCreditInfoDTO creditInfo;
        private List<RetrieveResponseDTO.Result> ragSupplementaryInfo;

        public EnterpriseInfo() {}

        public EnterpriseInfo(String companyName,
                            CompanyBasicInfoDTO basicInfo,
                            CompanyRiskInfoDTO riskInfo,
                            CompanyLawsuitInfoDTO lawsuitInfo,
                            CompanyCreditInfoDTO creditInfo,
                            List<RetrieveResponseDTO.Result> ragSupplementaryInfo) {
            this.companyName = companyName;
            this.basicInfo = basicInfo;
            this.riskInfo = riskInfo;
            this.lawsuitInfo = lawsuitInfo;
            this.creditInfo = creditInfo;
            this.ragSupplementaryInfo = ragSupplementaryInfo;
        }

        // Getters and Setters
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }

        public CompanyBasicInfoDTO getBasicInfo() { return basicInfo; }
        public void setBasicInfo(CompanyBasicInfoDTO basicInfo) { this.basicInfo = basicInfo; }

        public CompanyRiskInfoDTO getRiskInfo() { return riskInfo; }
        public void setRiskInfo(CompanyRiskInfoDTO riskInfo) { this.riskInfo = riskInfo; }

        public CompanyLawsuitInfoDTO getLawsuitInfo() { return lawsuitInfo; }
        public void setLawsuitInfo(CompanyLawsuitInfoDTO lawsuitInfo) { this.lawsuitInfo = lawsuitInfo; }

        public CompanyCreditInfoDTO getCreditInfo() { return creditInfo; }
        public void setCreditInfo(CompanyCreditInfoDTO creditInfo) { this.creditInfo = creditInfo; }

        public List<RetrieveResponseDTO.Result> getRagSupplementaryInfo() { return ragSupplementaryInfo; }
        public void setRagSupplementaryInfo(List<RetrieveResponseDTO.Result> ragSupplementaryInfo) { 
            this.ragSupplementaryInfo = ragSupplementaryInfo; 
        }

        /**
         * 获取企业整体风险等级
         */
        public String getOverallRiskLevel() {
            if (riskInfo != null && riskInfo.getRiskLevel() != null) {
                return riskInfo.getRiskLevel();
            }
            return "未知";
        }

        /**
         * 检查是否有高风险指标
         */
        public boolean hasHighRiskIndicators() {
            if (riskInfo != null) {
                return (riskInfo.getAbnormalCount() != null && riskInfo.getAbnormalCount() > 0) ||
                       (riskInfo.getPenaltyCount() != null && riskInfo.getPenaltyCount() > 0) ||
                       (riskInfo.getSeriousViolationCount() != null && riskInfo.getSeriousViolationCount() > 0);
            }
            if (lawsuitInfo != null) {
                return (lawsuitInfo.getDishonestyCount() != null && lawsuitInfo.getDishonestyCount() > 0) ||
                       (lawsuitInfo.getExecutedCount() != null && lawsuitInfo.getExecutedCount() > 0);
            }
            return false;
        }

        @Override
        public String toString() {
            return "EnterpriseInfo{" +
                "companyName='" + companyName + '\'' +
                ", hasBasicInfo=" + (basicInfo != null) +
                ", hasRiskInfo=" + (riskInfo != null) +
                ", hasLawsuitInfo=" + (lawsuitInfo != null) +
                ", hasCreditInfo=" + (creditInfo != null) +
                ", ragResultCount=" + (ragSupplementaryInfo != null ? ragSupplementaryInfo.size() : 0) +
                '}';
        }
    }
}
