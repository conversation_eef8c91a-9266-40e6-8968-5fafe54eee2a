/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：HistoricalCaseContext.java
 * 包    名：com.whiskerguard.ai.service.contract.context
 * 描    述：历史案例上下文管理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract.context;

import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 历史案例上下文管理器
 * <p>
 * 管理来自RAG检索的历史案例信息，
 * 包括租户历史审查案例和行业通用案例。
 */
public class HistoricalCaseContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 租户历史案例 */
    private List<RetrieveResponseDTO.Result> tenantCases = new ArrayList<>();

    /** 行业通用案例 */
    private List<RetrieveResponseDTO.Result> industryCases = new ArrayList<>();

    public HistoricalCaseContext() {}

    public HistoricalCaseContext(List<RetrieveResponseDTO.Result> tenantCases,
                               List<RetrieveResponseDTO.Result> industryCases) {
        this.tenantCases = tenantCases != null ? tenantCases : new ArrayList<>();
        this.industryCases = industryCases != null ? industryCases : new ArrayList<>();
    }

    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return tenantCases.isEmpty() && industryCases.isEmpty();
    }

    /**
     * 获取租户案例数量
     */
    public int getTenantCaseCount() {
        return tenantCases.size();
    }

    /**
     * 获取行业案例数量
     */
    public int getIndustryCaseCount() {
        return industryCases.size();
    }

    /**
     * 格式化租户历史案例
     */
    public String formatTenantCases() {
        if (tenantCases.isEmpty()) {
            return "暂无租户历史审查案例";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("**企业历史审查案例：**\n");
        
        for (int i = 0; i < Math.min(tenantCases.size(), 3); i++) {
            RetrieveResponseDTO.Result result = tenantCases.get(i);
            sb.append("案例").append(i + 1).append("：");
            sb.append(truncateText(result.getContent(), 200)).append("\n");
            
            if (result.getScore() != null) {
                sb.append("（相似度：").append(String.format("%.2f", result.getScore())).append("）\n");
            }
            sb.append("\n");
        }
        
        return sb.toString().trim();
    }

    /**
     * 格式化行业通用案例
     */
    public String formatIndustryCases() {
        if (industryCases.isEmpty()) {
            return "暂无相关行业案例";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("**行业通用案例参考：**\n");
        
        for (int i = 0; i < Math.min(industryCases.size(), 5); i++) {
            RetrieveResponseDTO.Result result = industryCases.get(i);
            sb.append("- ").append(truncateText(result.getContent(), 150)).append("\n");
            
            if (result.getScore() != null && result.getScore() > 0.8) {
                sb.append("  （高相关性案例）\n");
            }
        }
        
        return sb.toString().trim();
    }

    /**
     * 获取高相似度的租户案例
     */
    public List<RetrieveResponseDTO.Result> getHighSimilarityTenantCases(double threshold) {
        List<RetrieveResponseDTO.Result> highSimilarityCases = new ArrayList<>();
        
        for (RetrieveResponseDTO.Result result : tenantCases) {
            if (result.getScore() != null && result.getScore() >= threshold) {
                highSimilarityCases.add(result);
            }
        }
        
        return highSimilarityCases;
    }

    /**
     * 获取高相似度的行业案例
     */
    public List<RetrieveResponseDTO.Result> getHighSimilarityIndustryCases(double threshold) {
        List<RetrieveResponseDTO.Result> highSimilarityCases = new ArrayList<>();
        
        for (RetrieveResponseDTO.Result result : industryCases) {
            if (result.getScore() != null && result.getScore() >= threshold) {
                highSimilarityCases.add(result);
            }
        }
        
        return highSimilarityCases;
    }

    /**
     * 提取关键风险模式
     */
    public List<String> extractRiskPatterns() {
        List<String> riskPatterns = new ArrayList<>();
        
        // 从租户案例中提取风险模式
        for (RetrieveResponseDTO.Result result : tenantCases) {
            String content = result.getContent();
            if (content != null) {
                // 查找风险相关的关键词
                if (content.contains("风险") || content.contains("问题") || content.contains("违规")) {
                    String pattern = extractRiskPattern(content);
                    if (pattern != null && !riskPatterns.contains(pattern)) {
                        riskPatterns.add(pattern);
                    }
                }
            }
        }
        
        // 从行业案例中提取风险模式
        for (RetrieveResponseDTO.Result result : industryCases) {
            String content = result.getContent();
            if (content != null) {
                if (content.contains("风险") || content.contains("问题") || content.contains("违规")) {
                    String pattern = extractRiskPattern(content);
                    if (pattern != null && !riskPatterns.contains(pattern)) {
                        riskPatterns.add(pattern);
                    }
                }
            }
        }
        
        return riskPatterns;
    }

    /**
     * 从文本中提取风险模式
     */
    private String extractRiskPattern(String content) {
        // 简单的风险模式提取逻辑
        String[] sentences = content.split("[。！？]");
        
        for (String sentence : sentences) {
            if (sentence.contains("风险") || sentence.contains("问题") || sentence.contains("违规")) {
                return sentence.trim();
            }
        }
        
        return null;
    }

    /**
     * 获取最佳实践建议
     */
    public List<String> getBestPractices() {
        List<String> bestPractices = new ArrayList<>();
        
        // 从案例中提取最佳实践
        List<RetrieveResponseDTO.Result> allCases = new ArrayList<>();
        allCases.addAll(tenantCases);
        allCases.addAll(industryCases);
        
        for (RetrieveResponseDTO.Result result : allCases) {
            String content = result.getContent();
            if (content != null) {
                if (content.contains("建议") || content.contains("应当") || content.contains("最佳")) {
                    String practice = extractBestPractice(content);
                    if (practice != null && !bestPractices.contains(practice)) {
                        bestPractices.add(practice);
                    }
                }
            }
        }
        
        return bestPractices;
    }

    /**
     * 从文本中提取最佳实践
     */
    private String extractBestPractice(String content) {
        String[] sentences = content.split("[。！？]");
        
        for (String sentence : sentences) {
            if (sentence.contains("建议") || sentence.contains("应当") || sentence.contains("最佳")) {
                return sentence.trim();
            }
        }
        
        return null;
    }

    /**
     * 截断文本到指定长度
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "无";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + "...";
    }

    // Getters and Setters
    public List<RetrieveResponseDTO.Result> getTenantCases() {
        return tenantCases;
    }

    public void setTenantCases(List<RetrieveResponseDTO.Result> tenantCases) {
        this.tenantCases = tenantCases != null ? tenantCases : new ArrayList<>();
    }

    public List<RetrieveResponseDTO.Result> getIndustryCases() {
        return industryCases;
    }

    public void setIndustryCases(List<RetrieveResponseDTO.Result> industryCases) {
        this.industryCases = industryCases != null ? industryCases : new ArrayList<>();
    }

    @Override
    public String toString() {
        return "HistoricalCaseContext{" +
            "tenantCaseCount=" + tenantCases.size() +
            ", industryCaseCount=" + industryCases.size() +
            ", totalCaseCount=" + (tenantCases.size() + industryCases.size()) +
            '}';
    }
}
