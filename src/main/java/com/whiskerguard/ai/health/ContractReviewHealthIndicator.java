/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewHealthIndicator.java
 * 包    名：com.whiskerguard.ai.health
 * 描    述：合同智能审查健康检查器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.health;

import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.config.ContractReviewConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 合同智能审查健康检查器
 * <p>
 * 检查合同审查功能的健康状态，包括：
 * 1. 功能开关状态
 * 2. 依赖服务连通性
 * 3. 系统资源状态
 * 4. 配置有效性
 */
@Component("contractReviewHealthIndicator")
public class ContractReviewHealthIndicator implements HealthIndicator {

    private final Logger log = LoggerFactory.getLogger(ContractReviewHealthIndicator.class);

    private final ContractReviewConfiguration.ContractReviewProperties properties;
    private final RetrievalServiceClient retrievalServiceClient;
    private final GeneralServiceClient generalServiceClient;
    private final RegulatoryServiceClient regulatoryServiceClient;

    public ContractReviewHealthIndicator(
            ContractReviewConfiguration.ContractReviewProperties properties,
            RetrievalServiceClient retrievalServiceClient,
            GeneralServiceClient generalServiceClient,
            RegulatoryServiceClient regulatoryServiceClient) {
        this.properties = properties;
        this.retrievalServiceClient = retrievalServiceClient;
        this.generalServiceClient = generalServiceClient;
        this.regulatoryServiceClient = regulatoryServiceClient;
    }

    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 1. 检查功能开关
            if (!properties.isEnabled()) {
                return builder.down()
                    .withDetail("status", "DISABLED")
                    .withDetail("message", "合同审查功能已禁用")
                    .build();
            }

            // 2. 检查配置有效性
            boolean configValid = checkConfiguration();
            if (!configValid) {
                return builder.down()
                    .withDetail("status", "CONFIG_INVALID")
                    .withDetail("message", "配置参数无效")
                    .build();
            }

            // 3. 检查依赖服务健康状态
            ServiceHealthStatus serviceHealth = checkDependentServices();
            
            // 4. 检查系统资源
            SystemResourceStatus resourceStatus = checkSystemResources();

            // 5. 综合评估健康状态
            if (serviceHealth.isAllHealthy() && resourceStatus.isHealthy()) {
                builder.up()
                    .withDetail("status", "HEALTHY")
                    .withDetail("message", "合同审查功能运行正常");
            } else if (serviceHealth.hasCriticalFailure()) {
                builder.down()
                    .withDetail("status", "SERVICE_FAILURE")
                    .withDetail("message", "关键依赖服务不可用");
            } else {
                builder.up()
                    .withDetail("status", "DEGRADED")
                    .withDetail("message", "部分功能受限，但核心功能可用");
            }

            // 添加详细信息
            builder.withDetail("configuration", getConfigurationDetails())
                   .withDetail("services", serviceHealth.getDetails())
                   .withDetail("resources", resourceStatus.getDetails())
                   .withDetail("lastCheck", Instant.now());

        } catch (Exception e) {
            log.error("健康检查执行失败", e);
            builder.down()
                .withDetail("status", "CHECK_FAILED")
                .withDetail("message", "健康检查执行失败: " + e.getMessage())
                .withDetail("error", e.getClass().getSimpleName());
        }

        return builder.build();
    }

    /**
     * 检查配置有效性
     */
    private boolean checkConfiguration() {
        try {
            // 检查超时配置
            if (properties.getTimeout() <= 0 || properties.getTimeout() > 300000) {
                log.warn("审查超时配置无效: {}", properties.getTimeout());
                return false;
            }

            // 检查并发配置
            if (properties.getMaxConcurrent() <= 0 || properties.getMaxConcurrent() > 100) {
                log.warn("最大并发配置无效: {}", properties.getMaxConcurrent());
                return false;
            }

            // 检查内容长度配置
            if (properties.getMinContractLength() >= properties.getMaxContractLength()) {
                log.warn("合同长度配置无效: min={}, max={}", 
                    properties.getMinContractLength(), properties.getMaxContractLength());
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("配置检查失败", e);
            return false;
        }
    }

    /**
     * 检查依赖服务健康状态
     */
    private ServiceHealthStatus checkDependentServices() {
        ServiceHealthStatus status = new ServiceHealthStatus();
        
        // 检查RAG检索服务
        CompletableFuture<Boolean> ragHealthFuture = CompletableFuture.supplyAsync(() -> {
            try {
                // 这里可以调用一个简单的健康检查接口
                // 暂时返回true，实际应该调用服务的健康检查端点
                return true;
            } catch (Exception e) {
                log.warn("RAG服务健康检查失败", e);
                return false;
            }
        });

        // 检查天眼查服务
        CompletableFuture<Boolean> tianyanchaHealthFuture = CompletableFuture.supplyAsync(() -> {
            try {
                // 这里可以调用一个简单的健康检查接口
                return true;
            } catch (Exception e) {
                log.warn("天眼查服务健康检查失败", e);
                return false;
            }
        });

        // 检查法规制度服务
        CompletableFuture<Boolean> regulatoryHealthFuture = CompletableFuture.supplyAsync(() -> {
            try {
                // 这里可以调用一个简单的健康检查接口
                return true;
            } catch (Exception e) {
                log.warn("法规制度服务健康检查失败", e);
                return false;
            }
        });

        try {
            // 等待所有健康检查完成（最多5秒）
            boolean ragHealthy = ragHealthFuture.get(5, TimeUnit.SECONDS);
            boolean tianyanchaHealthy = tianyanchaHealthFuture.get(5, TimeUnit.SECONDS);
            boolean regulatoryHealthy = regulatoryHealthFuture.get(5, TimeUnit.SECONDS);

            status.setRagServiceHealthy(ragHealthy);
            status.setTianyanchaServiceHealthy(tianyanchaHealthy);
            status.setRegulatoryServiceHealthy(regulatoryHealthy);

        } catch (Exception e) {
            log.error("依赖服务健康检查超时", e);
            // 超时情况下标记为不健康
            status.setRagServiceHealthy(false);
            status.setTianyanchaServiceHealthy(false);
            status.setRegulatoryServiceHealthy(false);
        }

        return status;
    }

    /**
     * 检查系统资源状态
     */
    private SystemResourceStatus checkSystemResources() {
        SystemResourceStatus status = new SystemResourceStatus();
        
        try {
            // 检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsagePercent = (double) usedMemory / totalMemory * 100;
            
            status.setMemoryUsagePercent(memoryUsagePercent);
            status.setMemoryHealthy(memoryUsagePercent < 90); // 内存使用率超过90%认为不健康
            
            // 检查可用处理器数量
            int availableProcessors = runtime.availableProcessors();
            status.setAvailableProcessors(availableProcessors);
            status.setCpuHealthy(availableProcessors > 0);
            
            // 检查线程数量（简化检查）
            int activeThreadCount = Thread.activeCount();
            status.setActiveThreadCount(activeThreadCount);
            status.setThreadHealthy(activeThreadCount < 1000); // 活跃线程超过1000认为不健康
            
        } catch (Exception e) {
            log.error("系统资源检查失败", e);
            status.setMemoryHealthy(false);
            status.setCpuHealthy(false);
            status.setThreadHealthy(false);
        }
        
        return status;
    }

    /**
     * 获取配置详情
     */
    private Object getConfigurationDetails() {
        return new Object() {
            public boolean enabled = properties.isEnabled();
            public long timeout = properties.getTimeout();
            public int maxConcurrent = properties.getMaxConcurrent();
            public boolean enableDeepAnalysis = properties.isEnableDeepAnalysis();
            public boolean enableCompanyBackground = properties.isEnableCompanyBackground();
            public boolean enableInternalPolicyCheck = properties.isEnableInternalPolicyCheck();
            public boolean enableHistoricalCaseReference = properties.isEnableHistoricalCaseReference();
        };
    }

    /**
     * 服务健康状态类
     */
    private static class ServiceHealthStatus {
        private boolean ragServiceHealthy = false;
        private boolean tianyanchaServiceHealthy = false;
        private boolean regulatoryServiceHealthy = false;

        public boolean isAllHealthy() {
            return ragServiceHealthy && tianyanchaServiceHealthy && regulatoryServiceHealthy;
        }

        public boolean hasCriticalFailure() {
            // RAG服务是关键服务，如果不可用则认为是关键故障
            return !ragServiceHealthy;
        }

        public Object getDetails() {
            return new Object() {
                public boolean ragService = ragServiceHealthy;
                public boolean tianyanchaService = tianyanchaServiceHealthy;
                public boolean regulatoryService = regulatoryServiceHealthy;
            };
        }

        // Getters and Setters
        public void setRagServiceHealthy(boolean ragServiceHealthy) {
            this.ragServiceHealthy = ragServiceHealthy;
        }

        public void setTianyanchaServiceHealthy(boolean tianyanchaServiceHealthy) {
            this.tianyanchaServiceHealthy = tianyanchaServiceHealthy;
        }

        public void setRegulatoryServiceHealthy(boolean regulatoryServiceHealthy) {
            this.regulatoryServiceHealthy = regulatoryServiceHealthy;
        }
    }

    /**
     * 系统资源状态类
     */
    private static class SystemResourceStatus {
        private boolean memoryHealthy = true;
        private boolean cpuHealthy = true;
        private boolean threadHealthy = true;
        private double memoryUsagePercent = 0;
        private int availableProcessors = 0;
        private int activeThreadCount = 0;

        public boolean isHealthy() {
            return memoryHealthy && cpuHealthy && threadHealthy;
        }

        public Object getDetails() {
            return new Object() {
                public boolean memory = memoryHealthy;
                public boolean cpu = cpuHealthy;
                public boolean thread = threadHealthy;
                public double memoryUsage = memoryUsagePercent;
                public int processors = availableProcessors;
                public int threads = activeThreadCount;
            };
        }

        // Getters and Setters
        public void setMemoryHealthy(boolean memoryHealthy) {
            this.memoryHealthy = memoryHealthy;
        }

        public void setCpuHealthy(boolean cpuHealthy) {
            this.cpuHealthy = cpuHealthy;
        }

        public void setThreadHealthy(boolean threadHealthy) {
            this.threadHealthy = threadHealthy;
        }

        public void setMemoryUsagePercent(double memoryUsagePercent) {
            this.memoryUsagePercent = memoryUsagePercent;
        }

        public void setAvailableProcessors(int availableProcessors) {
            this.availableProcessors = availableProcessors;
        }

        public void setActiveThreadCount(int activeThreadCount) {
            this.activeThreadCount = activeThreadCount;
        }
    }
}
