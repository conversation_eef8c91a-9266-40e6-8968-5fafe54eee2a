package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.ContractRiskPointRepository;
import com.whiskerguard.ai.service.ContractRiskPointService;
import com.whiskerguard.ai.service.dto.ContractRiskPointDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.ContractRiskPoint}.
 */
@RestController
@RequestMapping("/api/contract-risk-points")
public class ContractRiskPointResource {

    private static final Logger LOG = LoggerFactory.getLogger(ContractRiskPointResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceContractRiskPoint";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ContractRiskPointService contractRiskPointService;

    private final ContractRiskPointRepository contractRiskPointRepository;

    public ContractRiskPointResource(
        ContractRiskPointService contractRiskPointService,
        ContractRiskPointRepository contractRiskPointRepository
    ) {
        this.contractRiskPointService = contractRiskPointService;
        this.contractRiskPointRepository = contractRiskPointRepository;
    }

    /**
     * {@code POST  /contract-risk-points} : Create a new contractRiskPoint.
     *
     * @param contractRiskPointDTO the contractRiskPointDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new contractRiskPointDTO, or with status {@code 400 (Bad Request)} if the contractRiskPoint has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<ContractRiskPointDTO> createContractRiskPoint(@Valid @RequestBody ContractRiskPointDTO contractRiskPointDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save ContractRiskPoint : {}", contractRiskPointDTO);
        if (contractRiskPointDTO.getId() != null) {
            throw new BadRequestAlertException("A new contractRiskPoint cannot already have an ID", ENTITY_NAME, "idexists");
        }
        contractRiskPointDTO = contractRiskPointService.save(contractRiskPointDTO);
        return ResponseEntity.created(new URI("/api/contract-risk-points/" + contractRiskPointDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, contractRiskPointDTO.getId().toString()))
            .body(contractRiskPointDTO);
    }

    /**
     * {@code PUT  /contract-risk-points/:id} : Updates an existing contractRiskPoint.
     *
     * @param id the id of the contractRiskPointDTO to save.
     * @param contractRiskPointDTO the contractRiskPointDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractRiskPointDTO,
     * or with status {@code 400 (Bad Request)} if the contractRiskPointDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the contractRiskPointDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<ContractRiskPointDTO> updateContractRiskPoint(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody ContractRiskPointDTO contractRiskPointDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update ContractRiskPoint : {}, {}", id, contractRiskPointDTO);
        if (contractRiskPointDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractRiskPointDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractRiskPointRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        contractRiskPointDTO = contractRiskPointService.update(contractRiskPointDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractRiskPointDTO.getId().toString()))
            .body(contractRiskPointDTO);
    }

    /**
     * {@code PATCH  /contract-risk-points/:id} : Partial updates given fields of an existing contractRiskPoint, field will ignore if it is null
     *
     * @param id the id of the contractRiskPointDTO to save.
     * @param contractRiskPointDTO the contractRiskPointDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractRiskPointDTO,
     * or with status {@code 400 (Bad Request)} if the contractRiskPointDTO is not valid,
     * or with status {@code 404 (Not Found)} if the contractRiskPointDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the contractRiskPointDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<ContractRiskPointDTO> partialUpdateContractRiskPoint(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody ContractRiskPointDTO contractRiskPointDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update ContractRiskPoint partially : {}, {}", id, contractRiskPointDTO);
        if (contractRiskPointDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractRiskPointDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractRiskPointRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<ContractRiskPointDTO> result = contractRiskPointService.partialUpdate(contractRiskPointDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractRiskPointDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /contract-risk-points} : get all the contractRiskPoints.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contractRiskPoints in body.
     */
    @GetMapping("")
    public ResponseEntity<List<ContractRiskPointDTO>> getAllContractRiskPoints(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of ContractRiskPoints");
        Page<ContractRiskPointDTO> page = contractRiskPointService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /contract-risk-points/:id} : get the "id" contractRiskPoint.
     *
     * @param id the id of the contractRiskPointDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the contractRiskPointDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ContractRiskPointDTO> getContractRiskPoint(@PathVariable("id") Long id) {
        LOG.debug("REST request to get ContractRiskPoint : {}", id);
        Optional<ContractRiskPointDTO> contractRiskPointDTO = contractRiskPointService.findOne(id);
        return ResponseUtil.wrapOrNotFound(contractRiskPointDTO);
    }

    /**
     * {@code DELETE  /contract-risk-points/:id} : delete the "id" contractRiskPoint.
     *
     * @param id the id of the contractRiskPointDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContractRiskPoint(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete ContractRiskPoint : {}", id);
        contractRiskPointService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
