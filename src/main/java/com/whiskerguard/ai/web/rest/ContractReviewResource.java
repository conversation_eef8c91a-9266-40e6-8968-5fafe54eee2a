package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.ContractReviewRepository;
import com.whiskerguard.ai.service.ContractReviewService;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.ContractReview}.
 */
@RestController
@RequestMapping("/api/contract-reviews")
public class ContractReviewResource {

    private static final Logger LOG = LoggerFactory.getLogger(ContractReviewResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceContractReview";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ContractReviewService contractReviewService;

    private final ContractReviewRepository contractReviewRepository;

    public ContractReviewResource(ContractReviewService contractReviewService, ContractReviewRepository contractReviewRepository) {
        this.contractReviewService = contractReviewService;
        this.contractReviewRepository = contractReviewRepository;
    }

    /**
     * {@code POST  /contract-reviews} : Create a new contractReview.
     *
     * @param contractReviewDTO the contractReviewDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new contractReviewDTO, or with status {@code 400 (Bad Request)} if the contractReview has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<ContractReviewDTO> createContractReview(@Valid @RequestBody ContractReviewDTO contractReviewDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save ContractReview : {}", contractReviewDTO);
        if (contractReviewDTO.getId() != null) {
            throw new BadRequestAlertException("A new contractReview cannot already have an ID", ENTITY_NAME, "idexists");
        }
        contractReviewDTO = contractReviewService.save(contractReviewDTO);
        return ResponseEntity.created(new URI("/api/contract-reviews/" + contractReviewDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, contractReviewDTO.getId().toString()))
            .body(contractReviewDTO);
    }

    /**
     * {@code PUT  /contract-reviews/:id} : Updates an existing contractReview.
     *
     * @param id the id of the contractReviewDTO to save.
     * @param contractReviewDTO the contractReviewDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractReviewDTO,
     * or with status {@code 400 (Bad Request)} if the contractReviewDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the contractReviewDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<ContractReviewDTO> updateContractReview(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody ContractReviewDTO contractReviewDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update ContractReview : {}, {}", id, contractReviewDTO);
        if (contractReviewDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractReviewDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractReviewRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        contractReviewDTO = contractReviewService.update(contractReviewDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractReviewDTO.getId().toString()))
            .body(contractReviewDTO);
    }

    /**
     * {@code PATCH  /contract-reviews/:id} : Partial updates given fields of an existing contractReview, field will ignore if it is null
     *
     * @param id the id of the contractReviewDTO to save.
     * @param contractReviewDTO the contractReviewDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractReviewDTO,
     * or with status {@code 400 (Bad Request)} if the contractReviewDTO is not valid,
     * or with status {@code 404 (Not Found)} if the contractReviewDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the contractReviewDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<ContractReviewDTO> partialUpdateContractReview(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody ContractReviewDTO contractReviewDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update ContractReview partially : {}, {}", id, contractReviewDTO);
        if (contractReviewDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractReviewDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractReviewRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<ContractReviewDTO> result = contractReviewService.partialUpdate(contractReviewDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractReviewDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /contract-reviews} : get all the contractReviews.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contractReviews in body.
     */
    @GetMapping("")
    public ResponseEntity<List<ContractReviewDTO>> getAllContractReviews(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of ContractReviews");
        Page<ContractReviewDTO> page = contractReviewService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /contract-reviews/:id} : get the "id" contractReview.
     *
     * @param id the id of the contractReviewDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the contractReviewDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ContractReviewDTO> getContractReview(@PathVariable("id") Long id) {
        LOG.debug("REST request to get ContractReview : {}", id);
        Optional<ContractReviewDTO> contractReviewDTO = contractReviewService.findOne(id);
        return ResponseUtil.wrapOrNotFound(contractReviewDTO);
    }

    /**
     * {@code DELETE  /contract-reviews/:id} : delete the "id" contractReview.
     *
     * @param id the id of the contractReviewDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContractReview(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete ContractReview : {}", id);
        contractReviewService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
