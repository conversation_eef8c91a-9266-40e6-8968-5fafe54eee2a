/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewExceptionHandler.java
 * 包    名：com.whiskerguard.ai.web.rest.errors
 * 描    述：合同智能审查异常处理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest.errors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

/**
 * 合同智能审查异常处理器
 * <p>
 * 专门处理合同审查相关的异常，提供统一的错误响应格式。
 */
@ControllerAdvice(basePackages = "com.whiskerguard.ai.web.rest")
public class ContractReviewExceptionHandler {

    private final Logger log = LoggerFactory.getLogger(ContractReviewExceptionHandler.class);

    /**
     * 处理合同审查异常
     */
    @ExceptionHandler(ContractReviewException.class)
    public ResponseEntity<ErrorResponse> handleContractReviewException(
            ContractReviewException ex, WebRequest request) {
        
        log.error("合同审查异常: {}", ex.getMessage(), ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
            "CONTRACT_REVIEW_ERROR",
            ex.getMessage(),
            HttpStatus.INTERNAL_SERVER_ERROR.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 处理超时异常
     */
    @ExceptionHandler(TimeoutException.class)
    public ResponseEntity<ErrorResponse> handleTimeoutException(
            TimeoutException ex, WebRequest request) {
        
        log.warn("合同审查超时: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "CONTRACT_REVIEW_TIMEOUT",
            "合同审查处理超时，请稍后重试",
            HttpStatus.REQUEST_TIMEOUT.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(errorResponse);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        log.warn("合同审查参数错误: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "INVALID_PARAMETER",
            ex.getMessage(),
            HttpStatus.BAD_REQUEST.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(SecurityException.class)
    public ResponseEntity<ErrorResponse> handleSecurityException(
            SecurityException ex, WebRequest request) {
        
        log.warn("合同审查权限不足: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "ACCESS_DENIED",
            "权限不足，无法访问该资源",
            HttpStatus.FORBIDDEN.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * 处理租户访问异常
     */
    @ExceptionHandler(TenantAccessException.class)
    public ResponseEntity<ErrorResponse> handleTenantAccessException(
            TenantAccessException ex, WebRequest request) {
        
        log.warn("租户访问异常: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "TENANT_ACCESS_DENIED",
            "租户访问权限验证失败",
            HttpStatus.FORBIDDEN.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * 处理服务不可用异常
     */
    @ExceptionHandler(ServiceUnavailableException.class)
    public ResponseEntity<ErrorResponse> handleServiceUnavailableException(
            ServiceUnavailableException ex, WebRequest request) {
        
        log.error("依赖服务不可用: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "SERVICE_UNAVAILABLE",
            "依赖服务暂时不可用，请稍后重试",
            HttpStatus.SERVICE_UNAVAILABLE.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, WebRequest request) {
        
        log.error("合同审查未知异常", ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
            "INTERNAL_SERVER_ERROR",
            "服务器内部错误，请联系管理员",
            HttpStatus.INTERNAL_SERVER_ERROR.value(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 错误响应类
     */
    public static class ErrorResponse {
        private String errorCode;
        private String message;
        private int status;
        private String path;
        private Instant timestamp;
        private Map<String, Object> details;

        public ErrorResponse(String errorCode, String message, int status, String path) {
            this.errorCode = errorCode;
            this.message = message;
            this.status = status;
            this.path = path;
            this.timestamp = Instant.now();
            this.details = new HashMap<>();
        }

        // Getters and Setters
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getStatus() { return status; }
        public void setStatus(int status) { this.status = status; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public Instant getTimestamp() { return timestamp; }
        public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }
        
        public Map<String, Object> getDetails() { return details; }
        public void setDetails(Map<String, Object> details) { this.details = details; }
        
        public void addDetail(String key, Object value) {
            this.details.put(key, value);
        }
    }

    /**
     * 合同审查异常
     */
    public static class ContractReviewException extends RuntimeException {
        public ContractReviewException(String message) {
            super(message);
        }
        
        public ContractReviewException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 租户访问异常
     */
    public static class TenantAccessException extends RuntimeException {
        public TenantAccessException(String message) {
            super(message);
        }
        
        public TenantAccessException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 服务不可用异常
     */
    public static class ServiceUnavailableException extends RuntimeException {
        public ServiceUnavailableException(String message) {
            super(message);
        }
        
        public ServiceUnavailableException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
