/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：CompanyRiskInfoDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：企业风险信息数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 企业风险信息数据传输对象
 * <p>
 * 用于封装从天眼查服务获取的企业风险评估信息，
 * 包括经营异常、行政处罚、严重违法失信等风险指标。
 */
public class CompanyRiskInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 企业名称 */
    private String companyName;

    /** 风险等级（HIGH/MEDIUM/LOW） */
    private String riskLevel;

    /** 风险分数（0-100） */
    private Integer riskScore;

    /** 经营异常记录数量 */
    private Integer abnormalCount;

    /** 行政处罚记录数量 */
    private Integer penaltyCount;

    /** 严重违法失信记录数量 */
    private Integer seriousViolationCount;

    /** 税务违法记录数量 */
    private Integer taxViolationCount;

    /** 环保违法记录数量 */
    private Integer environmentViolationCount;

    /** 欠税记录数量 */
    private Integer taxArrearageCount;

    /** 经营异常详情 */
    private List<AbnormalRecord> abnormalRecords;

    /** 行政处罚详情 */
    private List<PenaltyRecord> penaltyRecords;

    /** 严重违法失信详情 */
    private List<ViolationRecord> violationRecords;

    /** 风险评估摘要 */
    private String riskSummary;

    /** 主要风险因素 */
    private List<String> mainRiskFactors;

    /** 风险建议 */
    private List<String> riskRecommendations;

    /** 数据更新时间 */
    private LocalDate dataUpdateTime;

    public CompanyRiskInfoDTO() {}

    public CompanyRiskInfoDTO(String companyName, String riskLevel, Integer riskScore) {
        this.companyName = companyName;
        this.riskLevel = riskLevel;
        this.riskScore = riskScore;
    }

    // 内部类：经营异常记录
    public static class AbnormalRecord implements Serializable {
        private String reason;
        private LocalDate addDate;
        private LocalDate removeDate;
        private String status;
        private String authority;

        // Getters and Setters
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public LocalDate getAddDate() { return addDate; }
        public void setAddDate(LocalDate addDate) { this.addDate = addDate; }
        public LocalDate getRemoveDate() { return removeDate; }
        public void setRemoveDate(LocalDate removeDate) { this.removeDate = removeDate; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getAuthority() { return authority; }
        public void setAuthority(String authority) { this.authority = authority; }
    }

    // 内部类：行政处罚记录
    public static class PenaltyRecord implements Serializable {
        private String penaltyType;
        private String penaltyReason;
        private String penaltyAmount;
        private LocalDate penaltyDate;
        private String penaltyAuthority;
        private String penaltyNumber;

        // Getters and Setters
        public String getPenaltyType() { return penaltyType; }
        public void setPenaltyType(String penaltyType) { this.penaltyType = penaltyType; }
        public String getPenaltyReason() { return penaltyReason; }
        public void setPenaltyReason(String penaltyReason) { this.penaltyReason = penaltyReason; }
        public String getPenaltyAmount() { return penaltyAmount; }
        public void setPenaltyAmount(String penaltyAmount) { this.penaltyAmount = penaltyAmount; }
        public LocalDate getPenaltyDate() { return penaltyDate; }
        public void setPenaltyDate(LocalDate penaltyDate) { this.penaltyDate = penaltyDate; }
        public String getPenaltyAuthority() { return penaltyAuthority; }
        public void setPenaltyAuthority(String penaltyAuthority) { this.penaltyAuthority = penaltyAuthority; }
        public String getPenaltyNumber() { return penaltyNumber; }
        public void setPenaltyNumber(String penaltyNumber) { this.penaltyNumber = penaltyNumber; }
    }

    // 内部类：严重违法失信记录
    public static class ViolationRecord implements Serializable {
        private String violationType;
        private String violationReason;
        private LocalDate addDate;
        private LocalDate removeDate;
        private String status;
        private String authority;

        // Getters and Setters
        public String getViolationType() { return violationType; }
        public void setViolationType(String violationType) { this.violationType = violationType; }
        public String getViolationReason() { return violationReason; }
        public void setViolationReason(String violationReason) { this.violationReason = violationReason; }
        public LocalDate getAddDate() { return addDate; }
        public void setAddDate(LocalDate addDate) { this.addDate = addDate; }
        public LocalDate getRemoveDate() { return removeDate; }
        public void setRemoveDate(LocalDate removeDate) { this.removeDate = removeDate; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getAuthority() { return authority; }
        public void setAuthority(String authority) { this.authority = authority; }
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public Integer getAbnormalCount() {
        return abnormalCount;
    }

    public void setAbnormalCount(Integer abnormalCount) {
        this.abnormalCount = abnormalCount;
    }

    public Integer getPenaltyCount() {
        return penaltyCount;
    }

    public void setPenaltyCount(Integer penaltyCount) {
        this.penaltyCount = penaltyCount;
    }

    public Integer getSeriousViolationCount() {
        return seriousViolationCount;
    }

    public void setSeriousViolationCount(Integer seriousViolationCount) {
        this.seriousViolationCount = seriousViolationCount;
    }

    public Integer getTaxViolationCount() {
        return taxViolationCount;
    }

    public void setTaxViolationCount(Integer taxViolationCount) {
        this.taxViolationCount = taxViolationCount;
    }

    public Integer getEnvironmentViolationCount() {
        return environmentViolationCount;
    }

    public void setEnvironmentViolationCount(Integer environmentViolationCount) {
        this.environmentViolationCount = environmentViolationCount;
    }

    public Integer getTaxArrearageCount() {
        return taxArrearageCount;
    }

    public void setTaxArrearageCount(Integer taxArrearageCount) {
        this.taxArrearageCount = taxArrearageCount;
    }

    public List<AbnormalRecord> getAbnormalRecords() {
        return abnormalRecords;
    }

    public void setAbnormalRecords(List<AbnormalRecord> abnormalRecords) {
        this.abnormalRecords = abnormalRecords;
    }

    public List<PenaltyRecord> getPenaltyRecords() {
        return penaltyRecords;
    }

    public void setPenaltyRecords(List<PenaltyRecord> penaltyRecords) {
        this.penaltyRecords = penaltyRecords;
    }

    public List<ViolationRecord> getViolationRecords() {
        return violationRecords;
    }

    public void setViolationRecords(List<ViolationRecord> violationRecords) {
        this.violationRecords = violationRecords;
    }

    public String getRiskSummary() {
        return riskSummary;
    }

    public void setRiskSummary(String riskSummary) {
        this.riskSummary = riskSummary;
    }

    public List<String> getMainRiskFactors() {
        return mainRiskFactors;
    }

    public void setMainRiskFactors(List<String> mainRiskFactors) {
        this.mainRiskFactors = mainRiskFactors;
    }

    public List<String> getRiskRecommendations() {
        return riskRecommendations;
    }

    public void setRiskRecommendations(List<String> riskRecommendations) {
        this.riskRecommendations = riskRecommendations;
    }

    public LocalDate getDataUpdateTime() {
        return dataUpdateTime;
    }

    public void setDataUpdateTime(LocalDate dataUpdateTime) {
        this.dataUpdateTime = dataUpdateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyRiskInfoDTO that = (CompanyRiskInfoDTO) o;
        return Objects.equals(companyName, that.companyName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName);
    }

    @Override
    public String toString() {
        return "CompanyRiskInfoDTO{" +
            "companyName='" + companyName + '\'' +
            ", riskLevel='" + riskLevel + '\'' +
            ", riskScore=" + riskScore +
            ", abnormalCount=" + abnormalCount +
            ", penaltyCount=" + penaltyCount +
            ", seriousViolationCount=" + seriousViolationCount +
            '}';
    }
}
