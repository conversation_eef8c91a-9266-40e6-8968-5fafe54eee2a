/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceChecklistDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：合规检查清单数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 合规检查清单数据传输对象
 * <p>
 * 用于封装从法规制度服务获取的合规检查清单信息，
 * 提供标准化的合规检查项目和要求。
 */
public class ComplianceChecklistDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 清单ID */
    @NotNull
    private Long id;

    /** 租户ID */
    @NotNull
    private Long tenantId;

    /** 清单名称 */
    @NotBlank
    private String checklistName;

    /** 适用合同类型 */
    private String contractType;

    /** 适用行业 */
    private String industry;

    /** 清单版本 */
    private String version;

    /** 清单状态 */
    private String status;

    /** 检查项目列表 */
    private List<CheckItem> checkItems;

    /** 必检项目数量 */
    private Integer mandatoryItemCount;

    /** 建议检查项目数量 */
    private Integer recommendedItemCount;

    /** 清单描述 */
    private String description;

    /** 使用说明 */
    private String instructions;

    /** 创建日期 */
    private LocalDate createdDate;

    /** 最后更新日期 */
    private LocalDate lastUpdatedDate;

    /** 创建者 */
    private String creator;

    /** 审核者 */
    private String reviewer;

    public ComplianceChecklistDTO() {}

    public ComplianceChecklistDTO(Long id, Long tenantId, String checklistName, String contractType) {
        this.id = id;
        this.tenantId = tenantId;
        this.checklistName = checklistName;
        this.contractType = contractType;
    }

    // 内部类：检查项目
    public static class CheckItem implements Serializable {
        /** 项目ID */
        private Long itemId;

        /** 项目名称 */
        private String itemName;

        /** 项目描述 */
        private String description;

        /** 检查类别 */
        private String category;

        /** 是否必检 */
        private Boolean mandatory;

        /** 重要程度 (1-5) */
        private Integer importance;

        /** 检查要点 */
        private List<String> checkPoints;

        /** 法律依据 */
        private List<String> legalBasis;

        /** 风险等级 */
        private String riskLevel;

        /** 违规后果 */
        private String violationConsequences;

        /** 检查方法 */
        private String checkMethod;

        /** 参考标准 */
        private List<String> referenceStandards;

        /** 常见问题 */
        private List<String> commonIssues;

        /** 最佳实践 */
        private List<String> bestPractices;

        /** 相关文档 */
        private List<String> relatedDocuments;

        // Getters and Setters
        public Long getItemId() { return itemId; }
        public void setItemId(Long itemId) { this.itemId = itemId; }

        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public Boolean getMandatory() { return mandatory; }
        public void setMandatory(Boolean mandatory) { this.mandatory = mandatory; }

        public Integer getImportance() { return importance; }
        public void setImportance(Integer importance) { this.importance = importance; }

        public List<String> getCheckPoints() { return checkPoints; }
        public void setCheckPoints(List<String> checkPoints) { this.checkPoints = checkPoints; }

        public List<String> getLegalBasis() { return legalBasis; }
        public void setLegalBasis(List<String> legalBasis) { this.legalBasis = legalBasis; }

        public String getRiskLevel() { return riskLevel; }
        public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }

        public String getViolationConsequences() { return violationConsequences; }
        public void setViolationConsequences(String violationConsequences) { this.violationConsequences = violationConsequences; }

        public String getCheckMethod() { return checkMethod; }
        public void setCheckMethod(String checkMethod) { this.checkMethod = checkMethod; }

        public List<String> getReferenceStandards() { return referenceStandards; }
        public void setReferenceStandards(List<String> referenceStandards) { this.referenceStandards = referenceStandards; }

        public List<String> getCommonIssues() { return commonIssues; }
        public void setCommonIssues(List<String> commonIssues) { this.commonIssues = commonIssues; }

        public List<String> getBestPractices() { return bestPractices; }
        public void setBestPractices(List<String> bestPractices) { this.bestPractices = bestPractices; }

        public List<String> getRelatedDocuments() { return relatedDocuments; }
        public void setRelatedDocuments(List<String> relatedDocuments) { this.relatedDocuments = relatedDocuments; }

        @Override
        public String toString() {
            return "CheckItem{" +
                "itemId=" + itemId +
                ", itemName='" + itemName + '\'' +
                ", category='" + category + '\'' +
                ", mandatory=" + mandatory +
                ", importance=" + importance +
                ", riskLevel='" + riskLevel + '\'' +
                '}';
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getChecklistName() {
        return checklistName;
    }

    public void setChecklistName(String checklistName) {
        this.checklistName = checklistName;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<CheckItem> getCheckItems() {
        return checkItems;
    }

    public void setCheckItems(List<CheckItem> checkItems) {
        this.checkItems = checkItems;
    }

    public Integer getMandatoryItemCount() {
        return mandatoryItemCount;
    }

    public void setMandatoryItemCount(Integer mandatoryItemCount) {
        this.mandatoryItemCount = mandatoryItemCount;
    }

    public Integer getRecommendedItemCount() {
        return recommendedItemCount;
    }

    public void setRecommendedItemCount(Integer recommendedItemCount) {
        this.recommendedItemCount = recommendedItemCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getInstructions() {
        return instructions;
    }

    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDate lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ComplianceChecklistDTO that = (ComplianceChecklistDTO) o;
        return Objects.equals(id, that.id) && Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, tenantId);
    }

    @Override
    public String toString() {
        return "ComplianceChecklistDTO{" +
            "id=" + id +
            ", tenantId=" + tenantId +
            ", checklistName='" + checklistName + '\'' +
            ", contractType='" + contractType + '\'' +
            ", industry='" + industry + '\'' +
            ", version='" + version + '\'' +
            ", status='" + status + '\'' +
            ", mandatoryItemCount=" + mandatoryItemCount +
            ", recommendedItemCount=" + recommendedItemCount +
            '}';
    }
}
