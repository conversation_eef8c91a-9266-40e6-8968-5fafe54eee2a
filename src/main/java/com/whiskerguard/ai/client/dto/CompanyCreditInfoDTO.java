/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：CompanyCreditInfoDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：企业信用信息数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 企业信用信息数据传输对象
 * <p>
 * 用于封装从天眼查服务获取的企业信用状况信息，
 * 包括信用评级、失信记录、信用报告等信用相关数据。
 */
public class CompanyCreditInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 企业名称 */
    private String companyName;

    /** 信用等级 */
    private String creditLevel;

    /** 信用分数 */
    private Integer creditScore;

    /** 失信记录数量 */
    private Integer dishonestyCount;

    /** 信用评级机构 */
    private String ratingAgency;

    /** 评级日期 */
    private LocalDate ratingDate;

    /** 评级有效期 */
    private LocalDate ratingValidUntil;

    /** 信用状态 */
    private String creditStatus;

    /** 信用风险等级 */
    private String creditRiskLevel;

    /** 还款能力评估 */
    private String repaymentCapacity;

    /** 经营稳定性评估 */
    private String businessStability;

    /** 财务健康度 */
    private String financialHealth;

    /** 行业信用排名 */
    private Integer industryRanking;

    /** 信用历史记录 */
    private List<CreditHistoryRecord> creditHistory;

    /** 失信详情记录 */
    private List<DishonestyDetailRecord> dishonestyDetails;

    /** 信用评估报告 */
    private String creditAssessmentReport;

    /** 信用建议 */
    private List<String> creditRecommendations;

    /** 数据更新时间 */
    private LocalDate dataUpdateTime;

    public CompanyCreditInfoDTO() {}

    public CompanyCreditInfoDTO(String companyName, String creditLevel, Integer creditScore) {
        this.companyName = companyName;
        this.creditLevel = creditLevel;
        this.creditScore = creditScore;
    }

    // 内部类：信用历史记录
    public static class CreditHistoryRecord implements Serializable {
        private LocalDate recordDate;
        private String creditEvent;
        private String eventDescription;
        private String impact;
        private String source;

        // Getters and Setters
        public LocalDate getRecordDate() { return recordDate; }
        public void setRecordDate(LocalDate recordDate) { this.recordDate = recordDate; }
        public String getCreditEvent() { return creditEvent; }
        public void setCreditEvent(String creditEvent) { this.creditEvent = creditEvent; }
        public String getEventDescription() { return eventDescription; }
        public void setEventDescription(String eventDescription) { this.eventDescription = eventDescription; }
        public String getImpact() { return impact; }
        public void setImpact(String impact) { this.impact = impact; }
        public String getSource() { return source; }
        public void setSource(String source) { this.source = source; }
    }

    // 内部类：失信详情记录
    public static class DishonestyDetailRecord implements Serializable {
        private String dishonestyType;
        private String dishonestyReason;
        private LocalDate occurDate;
        private String amount;
        private String status;
        private String authority;
        private String caseNumber;

        // Getters and Setters
        public String getDishonestyType() { return dishonestyType; }
        public void setDishonestyType(String dishonestyType) { this.dishonestyType = dishonestyType; }
        public String getDishonestyReason() { return dishonestyReason; }
        public void setDishonestyReason(String dishonestyReason) { this.dishonestyReason = dishonestyReason; }
        public LocalDate getOccurDate() { return occurDate; }
        public void setOccurDate(LocalDate occurDate) { this.occurDate = occurDate; }
        public String getAmount() { return amount; }
        public void setAmount(String amount) { this.amount = amount; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getAuthority() { return authority; }
        public void setAuthority(String authority) { this.authority = authority; }
        public String getCaseNumber() { return caseNumber; }
        public void setCaseNumber(String caseNumber) { this.caseNumber = caseNumber; }
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCreditLevel() {
        return creditLevel;
    }

    public void setCreditLevel(String creditLevel) {
        this.creditLevel = creditLevel;
    }

    public Integer getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(Integer creditScore) {
        this.creditScore = creditScore;
    }

    public Integer getDishonestyCount() {
        return dishonestyCount;
    }

    public void setDishonestyCount(Integer dishonestyCount) {
        this.dishonestyCount = dishonestyCount;
    }

    public String getRatingAgency() {
        return ratingAgency;
    }

    public void setRatingAgency(String ratingAgency) {
        this.ratingAgency = ratingAgency;
    }

    public LocalDate getRatingDate() {
        return ratingDate;
    }

    public void setRatingDate(LocalDate ratingDate) {
        this.ratingDate = ratingDate;
    }

    public LocalDate getRatingValidUntil() {
        return ratingValidUntil;
    }

    public void setRatingValidUntil(LocalDate ratingValidUntil) {
        this.ratingValidUntil = ratingValidUntil;
    }

    public String getCreditStatus() {
        return creditStatus;
    }

    public void setCreditStatus(String creditStatus) {
        this.creditStatus = creditStatus;
    }

    public String getCreditRiskLevel() {
        return creditRiskLevel;
    }

    public void setCreditRiskLevel(String creditRiskLevel) {
        this.creditRiskLevel = creditRiskLevel;
    }

    public String getRepaymentCapacity() {
        return repaymentCapacity;
    }

    public void setRepaymentCapacity(String repaymentCapacity) {
        this.repaymentCapacity = repaymentCapacity;
    }

    public String getBusinessStability() {
        return businessStability;
    }

    public void setBusinessStability(String businessStability) {
        this.businessStability = businessStability;
    }

    public String getFinancialHealth() {
        return financialHealth;
    }

    public void setFinancialHealth(String financialHealth) {
        this.financialHealth = financialHealth;
    }

    public Integer getIndustryRanking() {
        return industryRanking;
    }

    public void setIndustryRanking(Integer industryRanking) {
        this.industryRanking = industryRanking;
    }

    public List<CreditHistoryRecord> getCreditHistory() {
        return creditHistory;
    }

    public void setCreditHistory(List<CreditHistoryRecord> creditHistory) {
        this.creditHistory = creditHistory;
    }

    public List<DishonestyDetailRecord> getDishonestyDetails() {
        return dishonestyDetails;
    }

    public void setDishonestyDetails(List<DishonestyDetailRecord> dishonestyDetails) {
        this.dishonestyDetails = dishonestyDetails;
    }

    public String getCreditAssessmentReport() {
        return creditAssessmentReport;
    }

    public void setCreditAssessmentReport(String creditAssessmentReport) {
        this.creditAssessmentReport = creditAssessmentReport;
    }

    public List<String> getCreditRecommendations() {
        return creditRecommendations;
    }

    public void setCreditRecommendations(List<String> creditRecommendations) {
        this.creditRecommendations = creditRecommendations;
    }

    public LocalDate getDataUpdateTime() {
        return dataUpdateTime;
    }

    public void setDataUpdateTime(LocalDate dataUpdateTime) {
        this.dataUpdateTime = dataUpdateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyCreditInfoDTO that = (CompanyCreditInfoDTO) o;
        return Objects.equals(companyName, that.companyName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName);
    }

    @Override
    public String toString() {
        return "CompanyCreditInfoDTO{" +
            "companyName='" + companyName + '\'' +
            ", creditLevel='" + creditLevel + '\'' +
            ", creditScore=" + creditScore +
            ", dishonestyCount=" + dishonestyCount +
            ", creditStatus='" + creditStatus + '\'' +
            ", creditRiskLevel='" + creditRiskLevel + '\'' +
            '}';
    }
}
