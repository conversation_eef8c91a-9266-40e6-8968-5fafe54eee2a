/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：InternalPolicyDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：企业内部制度数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 企业内部制度数据传输对象
 * <p>
 * 用于封装从法规制度服务获取的企业内部制度和政策信息，
 * 支持合同审查中的内部合规性检查。
 */
public class InternalPolicyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 制度ID */
    @NotNull
    private Long id;

    /** 租户ID */
    @NotNull
    private Long tenantId;

    /** 制度名称 */
    @NotBlank
    private String policyName;

    /** 制度编号 */
    private String policyNumber;

    /** 制度类别 */
    private String category;

    /** 制度类型 */
    private String policyType;

    /** 适用范围 */
    private String applicableScope;

    /** 制度级别 */
    private String policyLevel;

    /** 制度状态 */
    private String status;

    /** 制度摘要 */
    private String summary;

    /** 制度内容 */
    private String content;

    /** 关键要求 */
    private List<String> keyRequirements;

    /** 合规检查点 */
    private List<String> complianceCheckpoints;

    /** 违规后果 */
    private String violationConsequences;

    /** 相关法规 */
    private List<String> relatedRegulations;

    /** 适用部门 */
    private List<String> applicableDepartments;

    /** 适用岗位 */
    private List<String> applicablePositions;

    /** 制定部门 */
    private String formulatingDepartment;

    /** 审批人 */
    private String approver;

    /** 制定日期 */
    private LocalDate formulationDate;

    /** 生效日期 */
    private LocalDate effectiveDate;

    /** 失效日期 */
    private LocalDate expiryDate;

    /** 最后修订日期 */
    private LocalDate lastRevisionDate;

    /** 修订版本 */
    private String revisionVersion;

    /** 修订说明 */
    private String revisionNotes;

    /** 重要程度 (1-5) */
    private Integer importance;

    /** 是否强制执行 */
    private Boolean mandatory;

    /** 培训要求 */
    private String trainingRequirements;

    /** 监督检查频率 */
    private String supervisionFrequency;

    /** 附件文档 */
    private List<String> attachments;

    /** 备注 */
    private String remarks;

    public InternalPolicyDTO() {}

    public InternalPolicyDTO(Long id, Long tenantId, String policyName, String category) {
        this.id = id;
        this.tenantId = tenantId;
        this.policyName = policyName;
        this.category = category;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getPolicyName() {
        return policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getApplicableScope() {
        return applicableScope;
    }

    public void setApplicableScope(String applicableScope) {
        this.applicableScope = applicableScope;
    }

    public String getPolicyLevel() {
        return policyLevel;
    }

    public void setPolicyLevel(String policyLevel) {
        this.policyLevel = policyLevel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getKeyRequirements() {
        return keyRequirements;
    }

    public void setKeyRequirements(List<String> keyRequirements) {
        this.keyRequirements = keyRequirements;
    }

    public List<String> getComplianceCheckpoints() {
        return complianceCheckpoints;
    }

    public void setComplianceCheckpoints(List<String> complianceCheckpoints) {
        this.complianceCheckpoints = complianceCheckpoints;
    }

    public String getViolationConsequences() {
        return violationConsequences;
    }

    public void setViolationConsequences(String violationConsequences) {
        this.violationConsequences = violationConsequences;
    }

    public List<String> getRelatedRegulations() {
        return relatedRegulations;
    }

    public void setRelatedRegulations(List<String> relatedRegulations) {
        this.relatedRegulations = relatedRegulations;
    }

    public List<String> getApplicableDepartments() {
        return applicableDepartments;
    }

    public void setApplicableDepartments(List<String> applicableDepartments) {
        this.applicableDepartments = applicableDepartments;
    }

    public List<String> getApplicablePositions() {
        return applicablePositions;
    }

    public void setApplicablePositions(List<String> applicablePositions) {
        this.applicablePositions = applicablePositions;
    }

    public String getFormulatingDepartment() {
        return formulatingDepartment;
    }

    public void setFormulatingDepartment(String formulatingDepartment) {
        this.formulatingDepartment = formulatingDepartment;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public LocalDate getFormulationDate() {
        return formulationDate;
    }

    public void setFormulationDate(LocalDate formulationDate) {
        this.formulationDate = formulationDate;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public LocalDate getLastRevisionDate() {
        return lastRevisionDate;
    }

    public void setLastRevisionDate(LocalDate lastRevisionDate) {
        this.lastRevisionDate = lastRevisionDate;
    }

    public String getRevisionVersion() {
        return revisionVersion;
    }

    public void setRevisionVersion(String revisionVersion) {
        this.revisionVersion = revisionVersion;
    }

    public String getRevisionNotes() {
        return revisionNotes;
    }

    public void setRevisionNotes(String revisionNotes) {
        this.revisionNotes = revisionNotes;
    }

    public Integer getImportance() {
        return importance;
    }

    public void setImportance(Integer importance) {
        this.importance = importance;
    }

    public Boolean getMandatory() {
        return mandatory;
    }

    public void setMandatory(Boolean mandatory) {
        this.mandatory = mandatory;
    }

    public String getTrainingRequirements() {
        return trainingRequirements;
    }

    public void setTrainingRequirements(String trainingRequirements) {
        this.trainingRequirements = trainingRequirements;
    }

    public String getSupervisionFrequency() {
        return supervisionFrequency;
    }

    public void setSupervisionFrequency(String supervisionFrequency) {
        this.supervisionFrequency = supervisionFrequency;
    }

    public List<String> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<String> attachments) {
        this.attachments = attachments;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InternalPolicyDTO that = (InternalPolicyDTO) o;
        return Objects.equals(id, that.id) && Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, tenantId);
    }

    @Override
    public String toString() {
        return "InternalPolicyDTO{" +
            "id=" + id +
            ", tenantId=" + tenantId +
            ", policyName='" + policyName + '\'' +
            ", category='" + category + '\'' +
            ", policyType='" + policyType + '\'' +
            ", status='" + status + '\'' +
            ", importance=" + importance +
            ", mandatory=" + mandatory +
            '}';
    }
}
