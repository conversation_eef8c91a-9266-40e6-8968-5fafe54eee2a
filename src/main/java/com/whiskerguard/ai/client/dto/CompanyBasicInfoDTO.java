/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：CompanyBasicInfoDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：企业基本信息数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 企业基本信息数据传输对象
 * <p>
 * 用于封装从天眼查服务获取的企业基本工商信息，
 * 支持合同审查中的企业背景调查。
 */
public class CompanyBasicInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 企业名称 */
    @NotBlank
    private String companyName;

    /** 统一社会信用代码 */
    private String creditCode;

    /** 注册号 */
    private String registrationNumber;

    /** 法定代表人 */
    private String legalRepresentative;

    /** 注册资本 */
    private String registeredCapital;

    /** 实缴资本 */
    private String paidCapital;

    /** 成立日期 */
    private LocalDate establishDate;

    /** 经营状态 */
    private String status;

    /** 企业类型 */
    private String companyType;

    /** 所属行业 */
    private String industry;

    /** 注册地址 */
    private String registeredAddress;

    /** 经营范围 */
    private String businessScope;

    /** 营业期限开始 */
    private LocalDate businessTermStart;

    /** 营业期限结束 */
    private LocalDate businessTermEnd;

    /** 登记机关 */
    private String registrationAuthority;

    /** 核准日期 */
    private LocalDate approvalDate;

    /** 组织机构代码 */
    private String organizationCode;

    /** 纳税人识别号 */
    private String taxNumber;

    /** 企业规模 */
    private String companyScale;

    /** 员工人数 */
    private Integer employeeCount;

    /** 参保人数 */
    private Integer insuredCount;

    /** 联系电话 */
    private String contactPhone;

    /** 联系邮箱 */
    private String contactEmail;

    /** 官方网站 */
    private String website;

    /** 英文名称 */
    private String englishName;

    /** 曾用名 */
    private String formerNames;

    /** 数据更新时间 */
    private LocalDate dataUpdateTime;

    public CompanyBasicInfoDTO() {}

    public CompanyBasicInfoDTO(String companyName, String creditCode, String legalRepresentative) {
        this.companyName = companyName;
        this.creditCode = creditCode;
        this.legalRepresentative = legalRepresentative;
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public void setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
    }

    public String getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getPaidCapital() {
        return paidCapital;
    }

    public void setPaidCapital(String paidCapital) {
        this.paidCapital = paidCapital;
    }

    public LocalDate getEstablishDate() {
        return establishDate;
    }

    public void setEstablishDate(LocalDate establishDate) {
        this.establishDate = establishDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public LocalDate getBusinessTermStart() {
        return businessTermStart;
    }

    public void setBusinessTermStart(LocalDate businessTermStart) {
        this.businessTermStart = businessTermStart;
    }

    public LocalDate getBusinessTermEnd() {
        return businessTermEnd;
    }

    public void setBusinessTermEnd(LocalDate businessTermEnd) {
        this.businessTermEnd = businessTermEnd;
    }

    public String getRegistrationAuthority() {
        return registrationAuthority;
    }

    public void setRegistrationAuthority(String registrationAuthority) {
        this.registrationAuthority = registrationAuthority;
    }

    public LocalDate getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(LocalDate approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getCompanyScale() {
        return companyScale;
    }

    public void setCompanyScale(String companyScale) {
        this.companyScale = companyScale;
    }

    public Integer getEmployeeCount() {
        return employeeCount;
    }

    public void setEmployeeCount(Integer employeeCount) {
        this.employeeCount = employeeCount;
    }

    public Integer getInsuredCount() {
        return insuredCount;
    }

    public void setInsuredCount(Integer insuredCount) {
        this.insuredCount = insuredCount;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getFormerNames() {
        return formerNames;
    }

    public void setFormerNames(String formerNames) {
        this.formerNames = formerNames;
    }

    public LocalDate getDataUpdateTime() {
        return dataUpdateTime;
    }

    public void setDataUpdateTime(LocalDate dataUpdateTime) {
        this.dataUpdateTime = dataUpdateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyBasicInfoDTO that = (CompanyBasicInfoDTO) o;
        return Objects.equals(companyName, that.companyName) && 
               Objects.equals(creditCode, that.creditCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName, creditCode);
    }

    @Override
    public String toString() {
        return "CompanyBasicInfoDTO{" +
            "companyName='" + companyName + '\'' +
            ", creditCode='" + creditCode + '\'' +
            ", legalRepresentative='" + legalRepresentative + '\'' +
            ", registeredCapital='" + registeredCapital + '\'' +
            ", status='" + status + '\'' +
            ", industry='" + industry + '\'' +
            '}';
    }
}
