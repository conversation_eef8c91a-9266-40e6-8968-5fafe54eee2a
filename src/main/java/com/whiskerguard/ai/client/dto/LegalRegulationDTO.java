/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：LegalRegulationDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：法律法规数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 法律法规数据传输对象
 * <p>
 * 用于封装从法规制度服务获取的法律法规信息，
 * 支持合同审查中的法律合规性检查。
 */
public class LegalRegulationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 法规ID */
    @NotNull
    private Long id;

    /** 法规名称 */
    @NotBlank
    private String name;

    /** 法规编号 */
    private String regulationNumber;

    /** 法规类型（法律、行政法规、部门规章等） */
    private String regulationType;

    /** 发布机关 */
    private String issuingAuthority;

    /** 发布日期 */
    private LocalDate issueDate;

    /** 生效日期 */
    private LocalDate effectiveDate;

    /** 法规状态（有效、废止、修订等） */
    private String status;

    /** 适用行业 */
    private List<String> applicableIndustries;

    /** 适用合同类型 */
    private List<String> applicableContractTypes;

    /** 法规摘要 */
    private String summary;

    /** 关键条款 */
    private String keyProvisions;

    /** 法规全文 */
    private String fullText;

    /** 相关法规ID列表 */
    private List<Long> relatedRegulations;

    /** 重要程度（1-5，5为最重要） */
    private Integer importance;

    /** 风险等级 */
    private String riskLevel;

    /** 违规后果 */
    private String violationConsequences;

    /** 合规要点 */
    private List<String> compliancePoints;

    /** 参考案例 */
    private List<String> referenceCases;

    /** 更新时间 */
    private LocalDate lastUpdated;

    public LegalRegulationDTO() {}

    public LegalRegulationDTO(Long id, String name, String regulationType) {
        this.id = id;
        this.name = name;
        this.regulationType = regulationType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegulationNumber() {
        return regulationNumber;
    }

    public void setRegulationNumber(String regulationNumber) {
        this.regulationNumber = regulationNumber;
    }

    public String getRegulationType() {
        return regulationType;
    }

    public void setRegulationType(String regulationType) {
        this.regulationType = regulationType;
    }

    public String getIssuingAuthority() {
        return issuingAuthority;
    }

    public void setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getApplicableIndustries() {
        return applicableIndustries;
    }

    public void setApplicableIndustries(List<String> applicableIndustries) {
        this.applicableIndustries = applicableIndustries;
    }

    public List<String> getApplicableContractTypes() {
        return applicableContractTypes;
    }

    public void setApplicableContractTypes(List<String> applicableContractTypes) {
        this.applicableContractTypes = applicableContractTypes;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getKeyProvisions() {
        return keyProvisions;
    }

    public void setKeyProvisions(String keyProvisions) {
        this.keyProvisions = keyProvisions;
    }

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }

    public List<Long> getRelatedRegulations() {
        return relatedRegulations;
    }

    public void setRelatedRegulations(List<Long> relatedRegulations) {
        this.relatedRegulations = relatedRegulations;
    }

    public Integer getImportance() {
        return importance;
    }

    public void setImportance(Integer importance) {
        this.importance = importance;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getViolationConsequences() {
        return violationConsequences;
    }

    public void setViolationConsequences(String violationConsequences) {
        this.violationConsequences = violationConsequences;
    }

    public List<String> getCompliancePoints() {
        return compliancePoints;
    }

    public void setCompliancePoints(List<String> compliancePoints) {
        this.compliancePoints = compliancePoints;
    }

    public List<String> getReferenceCases() {
        return referenceCases;
    }

    public void setReferenceCases(List<String> referenceCases) {
        this.referenceCases = referenceCases;
    }

    public LocalDate getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDate lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LegalRegulationDTO that = (LegalRegulationDTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "LegalRegulationDTO{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", regulationNumber='" + regulationNumber + '\'' +
            ", regulationType='" + regulationType + '\'' +
            ", issuingAuthority='" + issuingAuthority + '\'' +
            ", status='" + status + '\'' +
            ", importance=" + importance +
            ", riskLevel='" + riskLevel + '\'' +
            '}';
    }
}
