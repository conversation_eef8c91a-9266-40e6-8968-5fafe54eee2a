/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：CompanyLawsuitInfoDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：企业诉讼信息数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 企业诉讼信息数据传输对象
 * <p>
 * 用于封装从天眼查服务获取的企业诉讼和法律纠纷信息，
 * 包括法律诉讼、仲裁案件、被执行人记录等司法风险信息。
 */
public class CompanyLawsuitInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 企业名称 */
    private String companyName;

    /** 法律诉讼总数 */
    private Integer lawsuitCount;

    /** 作为原告的案件数 */
    private Integer plaintiffCount;

    /** 作为被告的案件数 */
    private Integer defendantCount;

    /** 被执行人记录数 */
    private Integer executedCount;

    /** 失信被执行人记录数 */
    private Integer dishonestyCount;

    /** 限制高消费记录数 */
    private Integer restrictionCount;

    /** 仲裁案件数 */
    private Integer arbitrationCount;

    /** 法院公告数 */
    private Integer courtAnnouncementCount;

    /** 开庭公告数 */
    private Integer hearingAnnouncementCount;

    /** 诉讼详情列表 */
    private List<LawsuitRecord> lawsuitRecords;

    /** 被执行人详情列表 */
    private List<ExecutedRecord> executedRecords;

    /** 失信被执行人详情列表 */
    private List<DishonestyRecord> dishonestyRecords;

    /** 司法风险评估 */
    private String judicialRiskAssessment;

    /** 主要法律风险 */
    private List<String> mainLegalRisks;

    /** 数据更新时间 */
    private LocalDate dataUpdateTime;

    public CompanyLawsuitInfoDTO() {}

    public CompanyLawsuitInfoDTO(String companyName, Integer lawsuitCount) {
        this.companyName = companyName;
        this.lawsuitCount = lawsuitCount;
    }

    // 内部类：诉讼记录
    public static class LawsuitRecord implements Serializable {
        private String caseNumber;
        private String caseType;
        private String court;
        private LocalDate caseDate;
        private String plaintiff;
        private String defendant;
        private String caseReason;
        private String caseAmount;
        private String caseStatus;
        private String judgment;

        // Getters and Setters
        public String getCaseNumber() { return caseNumber; }
        public void setCaseNumber(String caseNumber) { this.caseNumber = caseNumber; }
        public String getCaseType() { return caseType; }
        public void setCaseType(String caseType) { this.caseType = caseType; }
        public String getCourt() { return court; }
        public void setCourt(String court) { this.court = court; }
        public LocalDate getCaseDate() { return caseDate; }
        public void setCaseDate(LocalDate caseDate) { this.caseDate = caseDate; }
        public String getPlaintiff() { return plaintiff; }
        public void setPlaintiff(String plaintiff) { this.plaintiff = plaintiff; }
        public String getDefendant() { return defendant; }
        public void setDefendant(String defendant) { this.defendant = defendant; }
        public String getCaseReason() { return caseReason; }
        public void setCaseReason(String caseReason) { this.caseReason = caseReason; }
        public String getCaseAmount() { return caseAmount; }
        public void setCaseAmount(String caseAmount) { this.caseAmount = caseAmount; }
        public String getCaseStatus() { return caseStatus; }
        public void setCaseStatus(String caseStatus) { this.caseStatus = caseStatus; }
        public String getJudgment() { return judgment; }
        public void setJudgment(String judgment) { this.judgment = judgment; }
    }

    // 内部类：被执行人记录
    public static class ExecutedRecord implements Serializable {
        private String caseNumber;
        private String court;
        private LocalDate executeDate;
        private String executeAmount;
        private String executeStatus;
        private String executeReason;

        // Getters and Setters
        public String getCaseNumber() { return caseNumber; }
        public void setCaseNumber(String caseNumber) { this.caseNumber = caseNumber; }
        public String getCourt() { return court; }
        public void setCourt(String court) { this.court = court; }
        public LocalDate getExecuteDate() { return executeDate; }
        public void setExecuteDate(LocalDate executeDate) { this.executeDate = executeDate; }
        public String getExecuteAmount() { return executeAmount; }
        public void setExecuteAmount(String executeAmount) { this.executeAmount = executeAmount; }
        public String getExecuteStatus() { return executeStatus; }
        public void setExecuteStatus(String executeStatus) { this.executeStatus = executeStatus; }
        public String getExecuteReason() { return executeReason; }
        public void setExecuteReason(String executeReason) { this.executeReason = executeReason; }
    }

    // 内部类：失信被执行人记录
    public static class DishonestyRecord implements Serializable {
        private String caseNumber;
        private String court;
        private LocalDate publishDate;
        private String dishonestyAmount;
        private String dishonestyReason;
        private String performance;
        private String status;

        // Getters and Setters
        public String getCaseNumber() { return caseNumber; }
        public void setCaseNumber(String caseNumber) { this.caseNumber = caseNumber; }
        public String getCourt() { return court; }
        public void setCourt(String court) { this.court = court; }
        public LocalDate getPublishDate() { return publishDate; }
        public void setPublishDate(LocalDate publishDate) { this.publishDate = publishDate; }
        public String getDishonestyAmount() { return dishonestyAmount; }
        public void setDishonestyAmount(String dishonestyAmount) { this.dishonestyAmount = dishonestyAmount; }
        public String getDishonestyReason() { return dishonestyReason; }
        public void setDishonestyReason(String dishonestyReason) { this.dishonestyReason = dishonestyReason; }
        public String getPerformance() { return performance; }
        public void setPerformance(String performance) { this.performance = performance; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getLawsuitCount() {
        return lawsuitCount;
    }

    public void setLawsuitCount(Integer lawsuitCount) {
        this.lawsuitCount = lawsuitCount;
    }

    public Integer getPlaintiffCount() {
        return plaintiffCount;
    }

    public void setPlaintiffCount(Integer plaintiffCount) {
        this.plaintiffCount = plaintiffCount;
    }

    public Integer getDefendantCount() {
        return defendantCount;
    }

    public void setDefendantCount(Integer defendantCount) {
        this.defendantCount = defendantCount;
    }

    public Integer getExecutedCount() {
        return executedCount;
    }

    public void setExecutedCount(Integer executedCount) {
        this.executedCount = executedCount;
    }

    public Integer getDishonestyCount() {
        return dishonestyCount;
    }

    public void setDishonestyCount(Integer dishonestyCount) {
        this.dishonestyCount = dishonestyCount;
    }

    public Integer getRestrictionCount() {
        return restrictionCount;
    }

    public void setRestrictionCount(Integer restrictionCount) {
        this.restrictionCount = restrictionCount;
    }

    public Integer getArbitrationCount() {
        return arbitrationCount;
    }

    public void setArbitrationCount(Integer arbitrationCount) {
        this.arbitrationCount = arbitrationCount;
    }

    public Integer getCourtAnnouncementCount() {
        return courtAnnouncementCount;
    }

    public void setCourtAnnouncementCount(Integer courtAnnouncementCount) {
        this.courtAnnouncementCount = courtAnnouncementCount;
    }

    public Integer getHearingAnnouncementCount() {
        return hearingAnnouncementCount;
    }

    public void setHearingAnnouncementCount(Integer hearingAnnouncementCount) {
        this.hearingAnnouncementCount = hearingAnnouncementCount;
    }

    public List<LawsuitRecord> getLawsuitRecords() {
        return lawsuitRecords;
    }

    public void setLawsuitRecords(List<LawsuitRecord> lawsuitRecords) {
        this.lawsuitRecords = lawsuitRecords;
    }

    public List<ExecutedRecord> getExecutedRecords() {
        return executedRecords;
    }

    public void setExecutedRecords(List<ExecutedRecord> executedRecords) {
        this.executedRecords = executedRecords;
    }

    public List<DishonestyRecord> getDishonestyRecords() {
        return dishonestyRecords;
    }

    public void setDishonestyRecords(List<DishonestyRecord> dishonestyRecords) {
        this.dishonestyRecords = dishonestyRecords;
    }

    public String getJudicialRiskAssessment() {
        return judicialRiskAssessment;
    }

    public void setJudicialRiskAssessment(String judicialRiskAssessment) {
        this.judicialRiskAssessment = judicialRiskAssessment;
    }

    public List<String> getMainLegalRisks() {
        return mainLegalRisks;
    }

    public void setMainLegalRisks(List<String> mainLegalRisks) {
        this.mainLegalRisks = mainLegalRisks;
    }

    public LocalDate getDataUpdateTime() {
        return dataUpdateTime;
    }

    public void setDataUpdateTime(LocalDate dataUpdateTime) {
        this.dataUpdateTime = dataUpdateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyLawsuitInfoDTO that = (CompanyLawsuitInfoDTO) o;
        return Objects.equals(companyName, that.companyName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName);
    }

    @Override
    public String toString() {
        return "CompanyLawsuitInfoDTO{" +
            "companyName='" + companyName + '\'' +
            ", lawsuitCount=" + lawsuitCount +
            ", plaintiffCount=" + plaintiffCount +
            ", defendantCount=" + defendantCount +
            ", executedCount=" + executedCount +
            ", dishonestyCount=" + dishonestyCount +
            '}';
    }
}
