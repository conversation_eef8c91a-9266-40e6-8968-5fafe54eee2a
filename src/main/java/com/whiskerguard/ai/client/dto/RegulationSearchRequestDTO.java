/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RegulationSearchRequestDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：法规搜索请求数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 法规搜索请求数据传输对象
 * <p>
 * 用于封装法规搜索的请求参数，
 * 支持多维度的法规检索和过滤。
 */
public class RegulationSearchRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @NotNull
    private Long tenantId;

    /** 搜索关键词 */
    @NotBlank
    private String keywords;

    /** 法规类型过滤 */
    private List<String> regulationTypes;

    /** 行业过滤 */
    private List<String> industries;

    /** 合同类型过滤 */
    private List<String> contractTypes;

    /** 发布机关过滤 */
    private List<String> issuingAuthorities;

    /** 法规状态过滤 */
    private List<String> statuses;

    /** 重要程度过滤 (1-5) */
    private Integer minImportance;

    /** 风险等级过滤 */
    private List<String> riskLevels;

    /** 生效日期范围 - 开始 */
    private LocalDate effectiveDateFrom;

    /** 生效日期范围 - 结束 */
    private LocalDate effectiveDateTo;

    /** 发布日期范围 - 开始 */
    private LocalDate issueDateFrom;

    /** 发布日期范围 - 结束 */
    private LocalDate issueDateTo;

    /** 搜索范围 */
    private SearchScope searchScope;

    /** 排序方式 */
    private SortOrder sortOrder;

    /** 返回结果数量限制 */
    private Integer limit;

    /** 分页偏移量 */
    private Integer offset;

    /** 是否包含全文内容 */
    private Boolean includeFullText;

    /** 是否包含相关法规 */
    private Boolean includeRelatedRegulations;

    /** 搜索模式 */
    private SearchMode searchMode;

    public RegulationSearchRequestDTO() {
        this.searchScope = SearchScope.ALL;
        this.sortOrder = SortOrder.RELEVANCE;
        this.limit = 20;
        this.offset = 0;
        this.includeFullText = false;
        this.includeRelatedRegulations = false;
        this.searchMode = SearchMode.FUZZY;
    }

    public RegulationSearchRequestDTO(Long tenantId, String keywords) {
        this();
        this.tenantId = tenantId;
        this.keywords = keywords;
    }

    // 枚举：搜索范围
    public enum SearchScope {
        ALL,           // 全部内容
        TITLE_ONLY,    // 仅标题
        SUMMARY_ONLY,  // 仅摘要
        CONTENT_ONLY,  // 仅正文
        KEY_PROVISIONS // 仅关键条款
    }

    // 枚举：排序方式
    public enum SortOrder {
        RELEVANCE,     // 相关性
        DATE_DESC,     // 日期降序
        DATE_ASC,      // 日期升序
        IMPORTANCE,    // 重要程度
        ALPHABETICAL   // 字母顺序
    }

    // 枚举：搜索模式
    public enum SearchMode {
        EXACT,         // 精确匹配
        FUZZY,         // 模糊匹配
        SEMANTIC       // 语义搜索
    }

    // Getters and Setters
    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public List<String> getRegulationTypes() {
        return regulationTypes;
    }

    public void setRegulationTypes(List<String> regulationTypes) {
        this.regulationTypes = regulationTypes;
    }

    public List<String> getIndustries() {
        return industries;
    }

    public void setIndustries(List<String> industries) {
        this.industries = industries;
    }

    public List<String> getContractTypes() {
        return contractTypes;
    }

    public void setContractTypes(List<String> contractTypes) {
        this.contractTypes = contractTypes;
    }

    public List<String> getIssuingAuthorities() {
        return issuingAuthorities;
    }

    public void setIssuingAuthorities(List<String> issuingAuthorities) {
        this.issuingAuthorities = issuingAuthorities;
    }

    public List<String> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<String> statuses) {
        this.statuses = statuses;
    }

    public Integer getMinImportance() {
        return minImportance;
    }

    public void setMinImportance(Integer minImportance) {
        this.minImportance = minImportance;
    }

    public List<String> getRiskLevels() {
        return riskLevels;
    }

    public void setRiskLevels(List<String> riskLevels) {
        this.riskLevels = riskLevels;
    }

    public LocalDate getEffectiveDateFrom() {
        return effectiveDateFrom;
    }

    public void setEffectiveDateFrom(LocalDate effectiveDateFrom) {
        this.effectiveDateFrom = effectiveDateFrom;
    }

    public LocalDate getEffectiveDateTo() {
        return effectiveDateTo;
    }

    public void setEffectiveDateTo(LocalDate effectiveDateTo) {
        this.effectiveDateTo = effectiveDateTo;
    }

    public LocalDate getIssueDateFrom() {
        return issueDateFrom;
    }

    public void setIssueDateFrom(LocalDate issueDateFrom) {
        this.issueDateFrom = issueDateFrom;
    }

    public LocalDate getIssueDateTo() {
        return issueDateTo;
    }

    public void setIssueDateTo(LocalDate issueDateTo) {
        this.issueDateTo = issueDateTo;
    }

    public SearchScope getSearchScope() {
        return searchScope;
    }

    public void setSearchScope(SearchScope searchScope) {
        this.searchScope = searchScope;
    }

    public SortOrder getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(SortOrder sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Boolean getIncludeFullText() {
        return includeFullText;
    }

    public void setIncludeFullText(Boolean includeFullText) {
        this.includeFullText = includeFullText;
    }

    public Boolean getIncludeRelatedRegulations() {
        return includeRelatedRegulations;
    }

    public void setIncludeRelatedRegulations(Boolean includeRelatedRegulations) {
        this.includeRelatedRegulations = includeRelatedRegulations;
    }

    public SearchMode getSearchMode() {
        return searchMode;
    }

    public void setSearchMode(SearchMode searchMode) {
        this.searchMode = searchMode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RegulationSearchRequestDTO that = (RegulationSearchRequestDTO) o;
        return Objects.equals(tenantId, that.tenantId) && 
               Objects.equals(keywords, that.keywords);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tenantId, keywords);
    }

    @Override
    public String toString() {
        return "RegulationSearchRequestDTO{" +
            "tenantId=" + tenantId +
            ", keywords='" + keywords + '\'' +
            ", regulationTypes=" + regulationTypes +
            ", industries=" + industries +
            ", contractTypes=" + contractTypes +
            ", searchScope=" + searchScope +
            ", sortOrder=" + sortOrder +
            ", limit=" + limit +
            ", searchMode=" + searchMode +
            '}';
    }
}
