/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：GeneralServiceClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：通用服务客户端接口（天眼查功能）
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.CompanyBasicInfoDTO;
import com.whiskerguard.ai.client.dto.CompanyCreditInfoDTO;
import com.whiskerguard.ai.client.dto.CompanyLawsuitInfoDTO;
import com.whiskerguard.ai.client.dto.CompanyRiskInfoDTO;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 通用服务客户端接口
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与通用服务（whiskerguard-general-service）进行通信。
 * 主要提供天眼查企业信息查询功能，支持合同审查中的企业背景调查。
 *
 * 主要功能：
 * 1. 查询企业基本工商信息
 * 2. 获取企业风险评估信息
 * 3. 查询企业诉讼和法律纠纷记录
 * 4. 获取企业信用状况和评级
 * 5. 支持多租户数据隔离
 */
@FeignClient(name = "whiskerguardgeneralservice", 
             configuration = GeneralServiceClient.GeneralServiceClientConfiguration.class)
public interface GeneralServiceClient {
    
    /**
     * 查询企业基本信息
     * <p>
     * 通过天眼查API获取企业的基本工商信息，
     * 包括注册资本、成立时间、经营状态、所属行业等。
     *
     * @param companyName 企业名称（完整或部分名称）
     * @param tenantId    租户ID，用于多租户数据隔离和计费
     * @return 企业基本信息DTO
     */
    @GetMapping("/api/tianyancha/company/basic/{companyName}")
    CompanyBasicInfoDTO getCompanyBasicInfo(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 查询企业风险信息
     * <p>
     * 获取企业的风险评估信息，包括经营异常、
     * 行政处罚、严重违法失信等风险指标。
     *
     * @param companyName 企业名称
     * @param tenantId    租户ID，用于多租户数据隔离
     * @return 企业风险信息DTO
     */
    @GetMapping("/api/tianyancha/company/risk/{companyName}")
    CompanyRiskInfoDTO getCompanyRiskInfo(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 查询企业诉讼信息
     * <p>
     * 获取企业涉及的法律诉讼、仲裁案件、
     * 被执行人记录等司法风险信息。
     *
     * @param companyName 企业名称
     * @param tenantId    租户ID，用于多租户数据隔离
     * @return 企业诉讼信息DTO
     */
    @GetMapping("/api/tianyancha/company/lawsuit/{companyName}")
    CompanyLawsuitInfoDTO getCompanyLawsuitInfo(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 查询企业信用信息
     * <p>
     * 获取企业的信用评级、失信记录、
     * 信用报告等信用状况信息。
     *
     * @param companyName 企业名称
     * @param tenantId    租户ID，用于多租户数据隔离
     * @return 企业信用信息DTO
     */
    @GetMapping("/api/tianyancha/company/credit/{companyName}")
    CompanyCreditInfoDTO getCompanyCreditInfo(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 查询企业股东信息
     * <p>
     * 获取企业的股权结构、主要股东、
     * 实际控制人等股权相关信息。
     *
     * @param companyName 企业名称
     * @param tenantId    租户ID，用于多租户数据隔离
     * @return 企业股东信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/shareholders/{companyName}")
    String getCompanyShareholderInfo(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 查询企业经营状况
     * <p>
     * 获取企业的经营范围、许可证信息、
     * 资质证书等经营相关信息。
     *
     * @param companyName 企业名称
     * @param tenantId    租户ID，用于多租户数据隔离
     * @return 企业经营状况信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/business/{companyName}")
    String getCompanyBusinessInfo(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 批量查询企业信息
     * <p>
     * 支持一次性查询多个企业的基本信息，
     * 提高合同审查中多关联方信息获取的效率。
     *
     * @param companyNames 企业名称列表（逗号分隔）
     * @param tenantId     租户ID，用于多租户数据隔离
     * @return 企业信息列表（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/batch")
    String batchGetCompanyInfo(
        @RequestParam("companyNames") String companyNames,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 检查企业名称有效性
     * <p>
     * 验证企业名称是否存在于天眼查数据库中，
     * 用于合同关联方提取结果的验证。
     *
     * @param companyName 企业名称
     * @param tenantId    租户ID，用于多租户数据隔离
     * @return 企业是否存在的布尔值
     */
    @GetMapping("/api/tianyancha/company/exists/{companyName}")
    Boolean checkCompanyExists(
        @PathVariable("companyName") String companyName,
        @RequestParam("tenantId") Long tenantId
    );
    
    /**
     * 通用服务客户端专用配置类
     * <p>
     * 配置认证拦截器和超时设置，
     * 优化天眼查API调用的性能和可靠性。
     */
    @Configuration
    class GeneralServiceClientConfiguration {
        
        /**
         * 配置用户认证拦截器
         * 自动传递JWT token和用户信息
         */
        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }
        
        /**
         * 配置Feign请求选项
         * 天眼查API调用可能需要较长时间，特别是批量查询
         */
        @Bean
        public Request.Options feignRequestOptions() {
            return new Request.Options(
                5000,  // 连接超时 5 秒
                15000  // 读取超时 15 秒 - 天眼查API响应可能较慢
            );
        }
    }
}
