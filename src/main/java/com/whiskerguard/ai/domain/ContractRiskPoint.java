package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.RiskCategory;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 风险点实体
 * 存储合同审查中识别的具体风险点
 */
@Entity
@Table(name = "contract_risk_point")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractRiskPoint implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 关联的审查记录ID
     */
    @NotNull
    @Column(name = "review_id", nullable = false)
    private Long reviewId;

    /**
     * 风险类别
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "risk_category", nullable = false)
    private RiskCategory riskCategory;

    /**
     * 风险描述
     */
    @Lob
    @Column(name = "risk_description", nullable = false)
    private String riskDescription;

    /**
     * 风险等级
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false)
    private RiskLevel severity;

    /**
     * 涉及的条款
     */
    @Lob
    @Column(name = "affected_clauses")
    private String affectedClauses;

    /**
     * 法律依据
     */
    @Lob
    @Column(name = "legal_basis")
    private String legalBasis;

    /**
     * 修改建议
     */
    @Lob
    @Column(name = "suggestions")
    private String suggestions;

    /**
     * 风险分数 (0-100)
     */
    @Min(value = 0)
    @Max(value = 100)
    @Column(name = "risk_score")
    private Integer riskScore;

    /**
     * 是否为关键风险
     */
    @Column(name = "is_critical")
    private Boolean isCritical;

    /**
     * 风险来源
     */
    @Size(max = 128)
    @Column(name = "risk_source", length = 128)
    private String riskSource;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "contractParties", "contractRiskPoints", "contractClauseIssues" }, allowSetters = true)
    private ContractReview review;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ContractRiskPoint id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ContractRiskPoint tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getReviewId() {
        return this.reviewId;
    }

    public ContractRiskPoint reviewId(Long reviewId) {
        this.setReviewId(reviewId);
        return this;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public RiskCategory getRiskCategory() {
        return this.riskCategory;
    }

    public ContractRiskPoint riskCategory(RiskCategory riskCategory) {
        this.setRiskCategory(riskCategory);
        return this;
    }

    public void setRiskCategory(RiskCategory riskCategory) {
        this.riskCategory = riskCategory;
    }

    public String getRiskDescription() {
        return this.riskDescription;
    }

    public ContractRiskPoint riskDescription(String riskDescription) {
        this.setRiskDescription(riskDescription);
        return this;
    }

    public void setRiskDescription(String riskDescription) {
        this.riskDescription = riskDescription;
    }

    public RiskLevel getSeverity() {
        return this.severity;
    }

    public ContractRiskPoint severity(RiskLevel severity) {
        this.setSeverity(severity);
        return this;
    }

    public void setSeverity(RiskLevel severity) {
        this.severity = severity;
    }

    public String getAffectedClauses() {
        return this.affectedClauses;
    }

    public ContractRiskPoint affectedClauses(String affectedClauses) {
        this.setAffectedClauses(affectedClauses);
        return this;
    }

    public void setAffectedClauses(String affectedClauses) {
        this.affectedClauses = affectedClauses;
    }

    public String getLegalBasis() {
        return this.legalBasis;
    }

    public ContractRiskPoint legalBasis(String legalBasis) {
        this.setLegalBasis(legalBasis);
        return this;
    }

    public void setLegalBasis(String legalBasis) {
        this.legalBasis = legalBasis;
    }

    public String getSuggestions() {
        return this.suggestions;
    }

    public ContractRiskPoint suggestions(String suggestions) {
        this.setSuggestions(suggestions);
        return this;
    }

    public void setSuggestions(String suggestions) {
        this.suggestions = suggestions;
    }

    public Integer getRiskScore() {
        return this.riskScore;
    }

    public ContractRiskPoint riskScore(Integer riskScore) {
        this.setRiskScore(riskScore);
        return this;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public Boolean getIsCritical() {
        return this.isCritical;
    }

    public ContractRiskPoint isCritical(Boolean isCritical) {
        this.setIsCritical(isCritical);
        return this;
    }

    public void setIsCritical(Boolean isCritical) {
        this.isCritical = isCritical;
    }

    public String getRiskSource() {
        return this.riskSource;
    }

    public ContractRiskPoint riskSource(String riskSource) {
        this.setRiskSource(riskSource);
        return this;
    }

    public void setRiskSource(String riskSource) {
        this.riskSource = riskSource;
    }

    public Integer getVersion() {
        return this.version;
    }

    public ContractRiskPoint version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public ContractRiskPoint createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public ContractRiskPoint updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public ContractRiskPoint isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public ContractReview getReview() {
        return this.review;
    }

    public void setReview(ContractReview contractReview) {
        this.review = contractReview;
    }

    public ContractRiskPoint review(ContractReview contractReview) {
        this.setReview(contractReview);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractRiskPoint)) {
            return false;
        }
        return getId() != null && getId().equals(((ContractRiskPoint) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractRiskPoint{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", reviewId=" + getReviewId() +
            ", riskCategory='" + getRiskCategory() + "'" +
            ", riskDescription='" + getRiskDescription() + "'" +
            ", severity='" + getSeverity() + "'" +
            ", affectedClauses='" + getAffectedClauses() + "'" +
            ", legalBasis='" + getLegalBasis() + "'" +
            ", suggestions='" + getSuggestions() + "'" +
            ", riskScore=" + getRiskScore() +
            ", isCritical='" + getIsCritical() + "'" +
            ", riskSource='" + getRiskSource() + "'" +
            ", version=" + getVersion() +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
