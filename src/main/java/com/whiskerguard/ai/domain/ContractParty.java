package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.PartyType;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 合同关联方实体
 * 存储从合同中提取的关联方信息
 */
@Entity
@Table(name = "contract_party")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractParty implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 关联的审查记录ID
     */
    @NotNull
    @Column(name = "review_id", nullable = false)
    private Long reviewId;

    /**
     * 关联方名称
     */
    @NotNull
    @Size(max = 256)
    @Column(name = "party_name", length = 256, nullable = false)
    private String partyName;

    /**
     * 关联方类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "party_type", nullable = false)
    private PartyType partyType;

    /**
     * 在合同中的角色
     */
    @Size(max = 64)
    @Column(name = "party_role", length = 64)
    private String partyRole;

    /**
     * 统一社会信用代码（企业）
     */
    @Size(max = 32)
    @Column(name = "credit_code", length = 32)
    private String creditCode;

    /**
     * 注册地址
     */
    @Size(max = 512)
    @Column(name = "registered_address", length = 512)
    private String registeredAddress;

    /**
     * 法定代表人（企业）
     */
    @Size(max = 64)
    @Column(name = "legal_representative", length = 64)
    private String legalRepresentative;

    /**
     * 联系方式
     */
    @Size(max = 256)
    @Column(name = "contact_info", length = 256)
    private String contactInfo;

    /**
     * 风险等级
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "risk_level")
    private RiskLevel riskLevel;

    /**
     * 风险因素（JSON数组）
     */
    @Lob
    @Column(name = "risk_factors")
    private String riskFactors;

    /**
     * 合规问题（JSON数组）
     */
    @Lob
    @Column(name = "compliance_issues")
    private String complianceIssues;

    /**
     * 天眼查信息（JSON格式）
     */
    @Lob
    @Column(name = "tianyancha_info")
    private String tianyanchaInfo;

    /**
     * 扩展信息
     */
    @Lob
    @Column(name = "additional_info")
    private String additionalInfo;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "contractParties", "contractRiskPoints", "contractClauseIssues" }, allowSetters = true)
    private ContractReview review;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ContractParty id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ContractParty tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getReviewId() {
        return this.reviewId;
    }

    public ContractParty reviewId(Long reviewId) {
        this.setReviewId(reviewId);
        return this;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public String getPartyName() {
        return this.partyName;
    }

    public ContractParty partyName(String partyName) {
        this.setPartyName(partyName);
        return this;
    }

    public void setPartyName(String partyName) {
        this.partyName = partyName;
    }

    public PartyType getPartyType() {
        return this.partyType;
    }

    public ContractParty partyType(PartyType partyType) {
        this.setPartyType(partyType);
        return this;
    }

    public void setPartyType(PartyType partyType) {
        this.partyType = partyType;
    }

    public String getPartyRole() {
        return this.partyRole;
    }

    public ContractParty partyRole(String partyRole) {
        this.setPartyRole(partyRole);
        return this;
    }

    public void setPartyRole(String partyRole) {
        this.partyRole = partyRole;
    }

    public String getCreditCode() {
        return this.creditCode;
    }

    public ContractParty creditCode(String creditCode) {
        this.setCreditCode(creditCode);
        return this;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getRegisteredAddress() {
        return this.registeredAddress;
    }

    public ContractParty registeredAddress(String registeredAddress) {
        this.setRegisteredAddress(registeredAddress);
        return this;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getLegalRepresentative() {
        return this.legalRepresentative;
    }

    public ContractParty legalRepresentative(String legalRepresentative) {
        this.setLegalRepresentative(legalRepresentative);
        return this;
    }

    public void setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
    }

    public String getContactInfo() {
        return this.contactInfo;
    }

    public ContractParty contactInfo(String contactInfo) {
        this.setContactInfo(contactInfo);
        return this;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public RiskLevel getRiskLevel() {
        return this.riskLevel;
    }

    public ContractParty riskLevel(RiskLevel riskLevel) {
        this.setRiskLevel(riskLevel);
        return this;
    }

    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRiskFactors() {
        return this.riskFactors;
    }

    public ContractParty riskFactors(String riskFactors) {
        this.setRiskFactors(riskFactors);
        return this;
    }

    public void setRiskFactors(String riskFactors) {
        this.riskFactors = riskFactors;
    }

    public String getComplianceIssues() {
        return this.complianceIssues;
    }

    public ContractParty complianceIssues(String complianceIssues) {
        this.setComplianceIssues(complianceIssues);
        return this;
    }

    public void setComplianceIssues(String complianceIssues) {
        this.complianceIssues = complianceIssues;
    }

    public String getTianyanchaInfo() {
        return this.tianyanchaInfo;
    }

    public ContractParty tianyanchaInfo(String tianyanchaInfo) {
        this.setTianyanchaInfo(tianyanchaInfo);
        return this;
    }

    public void setTianyanchaInfo(String tianyanchaInfo) {
        this.tianyanchaInfo = tianyanchaInfo;
    }

    public String getAdditionalInfo() {
        return this.additionalInfo;
    }

    public ContractParty additionalInfo(String additionalInfo) {
        this.setAdditionalInfo(additionalInfo);
        return this;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public Integer getVersion() {
        return this.version;
    }

    public ContractParty version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public ContractParty createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public ContractParty updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public ContractParty isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public ContractReview getReview() {
        return this.review;
    }

    public void setReview(ContractReview contractReview) {
        this.review = contractReview;
    }

    public ContractParty review(ContractReview contractReview) {
        this.setReview(contractReview);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractParty)) {
            return false;
        }
        return getId() != null && getId().equals(((ContractParty) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractParty{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", reviewId=" + getReviewId() +
            ", partyName='" + getPartyName() + "'" +
            ", partyType='" + getPartyType() + "'" +
            ", partyRole='" + getPartyRole() + "'" +
            ", creditCode='" + getCreditCode() + "'" +
            ", registeredAddress='" + getRegisteredAddress() + "'" +
            ", legalRepresentative='" + getLegalRepresentative() + "'" +
            ", contactInfo='" + getContactInfo() + "'" +
            ", riskLevel='" + getRiskLevel() + "'" +
            ", riskFactors='" + getRiskFactors() + "'" +
            ", complianceIssues='" + getComplianceIssues() + "'" +
            ", tianyanchaInfo='" + getTianyanchaInfo() + "'" +
            ", additionalInfo='" + getAdditionalInfo() + "'" +
            ", version=" + getVersion() +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
